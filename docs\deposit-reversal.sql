-- Anonimleştirilmiş ve parametreleştirilmiş SQL sorguları

-- 1. Balance Transaction silme
DELETE FROM BALANCE_TRANSACTION
WHERE ID = (
    SELECT ID FROM BALANCE_TRANSACTION
    WHERE USER_ID = (
        SELECT USER_ID FROM USER WHERE EMAIL = :email
    )
);

-- 2. Trade silme
DELETE FROM TRADE
WHERE ID = (
    SELECT ID FROM TRADE
    WHERE USER_ID = (
        SELECT USER_ID FROM USER WHERE EMAIL = :email
    )
);

-- 3. <PERSON><PERSON><PERSON> coin'e ait cüzdan kaydını silme (örnek coin: 'RZW')
DELETE FROM WALLET
WHERE ID = (
    SELECT ID FROM WALLET
    WHERE USER_ID = (
        SELECT USER_ID FROM USER WHERE EMAIL = :email
    )
    AND COIN_ID = (
        SELECT ID FROM MARKET WHERE COIN = 'RZW'
    )
);

-- 4. Payment kaydını güncelleme (örnek tutar: 200000)
UPDATE PAYMENT
SET LAST_ONLINE_DATE = NULL,
    PROCESS_STATUS = 'Submitted',
    STATUS = 0
WHERE ID = (
    SELECT ID FROM PAYMENT
    WHERE USER_ID = (
        SELECT USER_ID FROM USER WHERE EMAIL = :email
    )
    AND AMOUNT = 200000
    ORDER BY ID
    LIMIT 1
);

-- 5. Kullanıcı bakiyesini sıfırlama
UPDATE USER
SET BALANCE = 0
WHERE USER_ID = (
    SELECT USER_ID FROM USER WHERE EMAIL = :email
);
