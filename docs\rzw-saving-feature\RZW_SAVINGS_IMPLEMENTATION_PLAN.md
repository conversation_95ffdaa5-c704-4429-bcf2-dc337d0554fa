# RZW Vadeli Hesap Sistemi - Uygulama Planı

Bu dokümantasyon, RZW vadeli hesap sisteminin artırımlı olarak geliştirilmesi için hazırlanmış AI agent promptlarını içerir. Her faz bağımsız olarak tamamlanabilir ve test edilebilir.

---

# Faz 1: Database & Models - AI Agent Prompt

## 🎯 Görev Tanımı
RZW vadeli hesap sistemi için temel database yapısını oluştur. Mevcut sistemi etkilemeden yeni entity'ler ve migration'lar ekle.

## 📋 Yapılacak İşler

### 1.1 Wallet Entity Güncelleme
**Dosya**: `src/Areas/Admin/DbModel/Wallet.cs`

**Görev**: Mevcut Wallet entity'sine LOCKED_BALANCE kolonu ve hesaplanmış alanlar ekle.

```csharp
// Eklenecek kod:
[Required]
[Column("LOCKED_BALANCE", TypeName = "decimal(20,8)")]
public decimal LockedBalance { get; set; } = 0;

// Hesaplanmış alanlar
[NotMapped]
public decimal TotalBalance => Balance + LockedBalance;

[NotMapped]
public decimal AvailableBalance => Balance;
```

### 1.2 RZW Savings Entity'leri Oluştur

#### 1.2.1 RzwSavingsAccount Entity
**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsAccount.cs`

**Görev**: Vadeli hesap bilgilerini tutan entity oluştur.

**Özellikler**:
- UserId, RzwAmount, InterestRate, TermType, TermDuration
- StartDate, MaturityDate, Status, TotalEarnedRzw
- LastInterestDate, AutoRenew, EarlyWithdrawalPenalty
- User navigation property, InterestPayments collection

#### 1.2.2 RzwSavingsInterestPayment Entity
**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsInterestPayment.cs`

**Görev**: Faiz ödeme kayıtlarını tutan entity oluştur.

**Özellikler**:
- RzwSavingsAccountId, RzwAmount, PaymentDate
- WalletTransactionId, Description, CreatedDate
- RzwSavingsAccount navigation property

#### 1.2.3 RzwSavingsPlan Entity
**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsPlan.cs`

**Görev**: Vadeli hesap planlarını tutan entity oluştur.

**Özellikler**:
- Name, TermType, TermDuration, InterestRate
- MinRzwAmount, MaxRzwAmount, IsActive, DisplayOrder
- Description, CreatedDate, ModifiedDate

### 1.3 Enums ve Constants
**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsEnums.cs`

**Görev**: RZW savings için gerekli enum'lar ve sabitler oluştur.

```csharp
public static class RzwSavingsStatus
{
    public const string Active = "Active";
    public const string Matured = "Matured";
    public const string Withdrawn = "Withdrawn";
    public const string Cancelled = "Cancelled";
}

public static class RzwSavingsTermType
{
    public const string Daily = "Daily";
    public const string Monthly = "Monthly";
    public const string Yearly = "Yearly";
}

public static class RzwSavingsConstants
{
    public const decimal DEFAULT_EARLY_WITHDRAWAL_PENALTY = 0.10m; // %10
    public const int DAILY_TERM_DURATION = 1;
    public const int MONTHLY_TERM_DURATION = 30;
    public const int YEARLY_TERM_DURATION = 365;
}
```

### 1.4 Trade Entity Güncellemeleri

#### 1.4.1 TradeType Enum Güncelleme
**Dosya**: `src/Areas/Admin/DbModel/Trade.cs`

**Görev**: TradeType enum'a RZW savings için yeni değerler ekle.

```csharp
public enum TradeType
{
    Buy,                    // alis
    Sell,                   // satis
    PackageBonus,           // paket satın alımında verilen bonus
    ReferralReward,         // yönlendirme ödülü

    // YENİ: RZW Savings types
    RzwSavingsDeposit,      // vadeli hesaba yatırma
    RzwSavingsWithdrawal,   // vadeli hesaptan çekme
    RzwSavingsInterest,     // faiz ödemesi
    RzwSavingsEarlyWithdrawal, // erken çekim
    RzwSavingsMaturity      // vade dolma
}
```

#### 1.4.2 Trade Entity'ye RzwSavingsAccountId Ekleme
**Dosya**: `src/Areas/Admin/DbModel/Trade.cs`

**Görev**: Trade entity'sine RzwSavingsAccountId kolonu ve navigation property ekle.

```csharp
// Eklenecek property:
[Column("RZW_SAVINGS_ACCOUNT_ID")]
public int? RzwSavingsAccountId { get; set; }

// Eklenecek navigation property:
[ForeignKey("RzwSavingsAccountId")]
public virtual RzwSavingsAccount? RzwSavingsAccount { get; set; }
```

### 1.5 AppDbContext Güncelleme
**Dosya**: `src/Areas/Admin/DbModel/AppDbContext.cs`

**Görev**: Yeni entity'ler için DbSet'leri ekle.

```csharp
// Eklenecek DbSet'ler:
public virtual DbSet<RzwSavingsAccount> RzwSavingsAccounts { get; set; }
public virtual DbSet<RzwSavingsInterestPayment> RzwSavingsInterestPayments { get; set; }
public virtual DbSet<RzwSavingsPlan> RzwSavingsPlans { get; set; }
```

### 1.6 Migration Oluşturma

**Görev**: Database değişiklikleri için migration oluştur.

**Migration adı**: `AddRzwSavingsSupport`

**Değişiklikler**:
1. WALLET tablosuna LOCKED_BALANCE kolonu ekle (decimal(20,8), default: 0)
2. RZW_SAVINGS_PLAN tablosu oluştur
3. RZW_SAVINGS_ACCOUNT tablosu oluştur
4. RZW_SAVINGS_INTEREST_PAYMENT tablosu oluştur
5. TRADE tablosuna RZW_SAVINGS_ACCOUNT_ID kolonu ekle
6. Gerekli index'leri ekle

## ✅ Başarı Kriterleri

1. **Migration başarıyla çalışıyor**
2. **Yeni tablolar oluşturuluyor**
3. **LOCKED_BALANCE kolonu ekleniyor**
4. **Foreign key'ler doğru çalışıyor**
5. **Mevcut veriler korunuyor**
6. **Build hataları yok**

## 🔧 Test Komutları

```bash
# Migration oluştur
dotnet ef migrations add AddRzwSavingsSupport

# Migration'ı uygula
dotnet ef database update

# Build kontrolü
dotnet build
```

## 📝 Önemli Notlar

- **Mevcut sistemi etkileme**: BALANCE kolonu değişmeyecek
- **Backward compatibility**: Mevcut kod çalışmaya devam edecek
- **Varsayılan değerler**: LOCKED_BALANCE = 0
- **Naming convention**: Mevcut proje standartlarını takip et
- **Data types**: decimal(20,8) para birimleri için

---

# Faz 2: Balance Management - AI Agent Prompt

## 🎯 Görev Tanımı
RZW bakiye yönetimi servisleri oluştur. Mevcut WalletService'i genişlet ve RZW-specific balance operations ekle. Backward compatibility sağla.

## 📋 Yapılacak İşler

### 2.1 WalletService Interface Genişletme
**Dosya**: `src/Areas/Admin/Services/Interfaces/IWalletService.cs`

**Görev**: Mevcut interface'e yeni metotlar ekle, eski metotları Obsolete işaretle.

**Eklenecek metotlar**:
```csharp
// BACKWARD COMPATIBILITY - Obsolete metotlar
[Obsolete("Use GetUserAvailableBalanceAsync instead")]
Task<decimal> GetUserBalanceAsync(int userId, int coinId);

[Obsolete("Use AddAvailableBalanceAsync instead")]
Task<Wallet> AddBalanceAsync(int userId, int coinId, decimal amount);

[Obsolete("Use DeductAvailableBalanceAsync instead")]
Task<bool> DeductBalanceAsync(int userId, int coinId, decimal amount);

// YENİ METOTLAR - Available Balance
Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId);
Task<Wallet> AddAvailableBalanceAsync(int userId, int coinId, decimal amount);
Task<bool> DeductAvailableBalanceAsync(int userId, int coinId, decimal amount);

// YENİ METOTLAR - Locked Balance
Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId);
Task<bool> LockBalanceAsync(int userId, int coinId, decimal amount);
Task<bool> UnlockBalanceAsync(int userId, int coinId, decimal amount);

// YENİ METOTLAR - Total Balance
Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId);

// YENİ METOTLAR - Balance Info
Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId);
Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId);
```

### 2.2 WalletService Implementation Güncelleme
**Dosya**: `src/Areas/Admin/Services/WalletService.cs`

**Görev**:
1. Constructor'a TradeService ve ITokenPriceService dependency ekle
2. Obsolete metotları implement et (backward compatibility)
3. Yeni balance management metotlarını implement et
4. Her wallet değişikliğinde Trade kaydı oluştur

**Önemli**:
- Available balance = BALANCE kolonu
- Locked balance = LOCKED_BALANCE kolonu
- Total balance = Available + Locked (hesaplanmış)
- Tüm işlemlerde TradeService ile audit trail oluştur

### 2.3 Balance Info Models Oluştur

#### 2.3.1 WalletBalanceInfo Model
**Dosya**: `src/Models/WalletBalanceInfo.cs`

```csharp
public class WalletBalanceInfo
{
    public int UserId { get; set; }
    public int CoinId { get; set; }
    public string CoinCode { get; set; } = string.Empty;
    public string CoinName { get; set; } = string.Empty;
    public string IconUrl { get; set; } = string.Empty;
    public decimal AvailableBalance { get; set; }
    public decimal LockedBalance { get; set; }
    public decimal TotalBalance => AvailableBalance + LockedBalance;

    // RZW özel alanlar
    public decimal? LockedInSavings { get; set; }
    public List<SavingsLockInfo>? SavingsLocks { get; set; }
}

public class SavingsLockInfo
{
    public int SavingsAccountId { get; set; }
    public decimal Amount { get; set; }
    public DateTime MaturityDate { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public decimal InterestRate { get; set; }
}
```

#### 2.3.2 RzwBalanceInfo Model
**Dosya**: `src/Models/RzwBalanceInfo.cs`

```csharp
public class RzwBalanceInfo
{
    public int UserId { get; set; }
    public decimal AvailableRzw { get; set; }
    public decimal LockedRzw { get; set; }
    public decimal TotalRzw => AvailableRzw + LockedRzw;
    public decimal LockedInSavings { get; set; }
    public int ActiveSavingsCount { get; set; }
    public List<SavingsLockInfo> SavingsLocks { get; set; } = new();
}
```

### 2.4 RzwBalanceManagementService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwBalanceManagementService.cs`

**Görev**: RZW-specific balance operations için özel service oluştur.

**Dependencies**:
- AppDbContext
- IWalletService
- ITokenPriceService (RZW token ID için)
- TradeService (audit trail için)

**Metotlar**:
```csharp
// RZW Token ID
Task<int> GetRzwTokenIdAsync();

// RZW Balance Checks
Task<bool> HasSufficientAvailableRzwAsync(int userId, decimal amount);
Task<RzwBalanceInfo> GetRzwBalanceInfoAsync(int userId);

// RZW Savings Operations
Task<bool> LockRzwForSavingsAsync(int userId, decimal amount, string description);
Task<bool> UnlockRzwFromSavingsAsync(int userId, decimal amount, string description);
Task<bool> AddRzwInterestAsync(int userId, decimal amount, string description);
Task<bool> DeductAvailableRzwAsync(int userId, decimal amount);
```

## ✅ Başarı Kriterleri

1. **Backward compatibility**: Eski metotlar çalışıyor
2. **Balance operations**: Lock/unlock işlemleri doğru çalışıyor
3. **Trade records**: Her wallet değişikliğinde Trade kaydı oluşuyor
4. **RZW operations**: RZW-specific işlemler çalışıyor
5. **TokenPriceService**: RZW token ID dinamik olarak alınıyor
6. **Build hataları yok**

## 🔧 Test Komutları

```bash
# Build kontrolü
dotnet build

# Test senaryoları
# 1. Kullanıcının RZW balance'ını kontrol et
# 2. RZW lock/unlock işlemlerini test et
# 3. Trade kayıtlarının oluştuğunu kontrol et
```

## 📝 Önemli Notlar

- **Mevcut kodu bozmama**: Obsolete metotlar eski kodu destekler
- **TokenPriceService kullan**: RZW token ID için sabit değer kullanma
- **Trade audit**: Her wallet değişikliğinde Trade kaydı oluştur
- **Transaction safety**: Database transaction'ları kullan
- **Error handling**: Tüm işlemlerde hata kontrolü yap

---

# Faz 3: Savings System Core - AI Agent Prompt ✅ **TAMAMLANDI**

## 🎯 Görev Tanımı
RZW vadeli hesap sisteminin core business logic'ini oluştur. Vadeli hesap açma, faiz hesaplama, erken çekim ve vade dolma işlemlerini implement et.

## ✅ **FAZ 3 TAMAMLANDI** ✅

### 🎯 **Implementation Özeti:**

#### Tamamlanan Servisler:
- ✅ **RzwSavingsPlanService** - Plan yönetimi ve validasyon
- ✅ **RzwSavingsInterestService** - Bileşik faiz hesaplama ve ödeme işlemleri
- ✅ **RzwSavingsService** - Ana vadeli hesap servisi (açma/kapatma/erken çekim)
- ✅ **Service registrations** - Program.cs'e kayıtlar eklendi

#### Teknik Özellikler:
- ✅ **Compound Interest Algorithm**: P * (1 + r)^n - P formülü implementasyonu
- ✅ **Early Withdrawal Logic**: Holding period bazlı faiz hesaplama
- ✅ **Plan Management**: Dynamic plan validation ve initialization
- ✅ **Balance Integration**: RzwBalanceManagementService entegrasyonu
- ✅ **Transaction Safety**: Database transaction güvenliği
- ✅ **Error Handling**: Comprehensive hata yönetimi ve logging

#### Unit Test Coverage:
- ✅ **RzwSavingsPlanServiceTests**: 8/8 tests passing (%100)
- ✅ **RzwSavingsInterestServiceTests**: 11/11 tests passing (%100)
- ✅ **RzwSavingsServiceTests**: 7/7 tests passing (%100)
- ✅ **Toplam**: 26/26 test geçiyor (%100 başarı)

#### Oluşturulan Dosyalar:
- ✅ `src/Areas/Admin/Services/RzwSavingsPlanService.cs`
- ✅ `src/Areas/Admin/Services/RzwSavingsInterestService.cs`
- ✅ `src/Areas/Admin/Services/RzwSavingsService.cs`
- ✅ `src_unittests/Tests/Services/RzwSavingsPlanServiceTests.cs`
- ✅ `src_unittests/Tests/Services/RzwSavingsInterestServiceTests.cs`
- ✅ `src_unittests/Tests/Services/RzwSavingsServiceTests.cs`

#### Çözülen Teknik Sorunlar:
- ✅ **InMemory Database Transaction Warning** - ConfigureWarnings ile çözüldü
- ✅ **Service Dependencies** - Proper dependency injection implementasyonu
- ✅ **Compound Interest Calculation** - Matematiksel formül doğrulaması
- ✅ **Early Withdrawal Logic** - Eligible plan selection algoritması

### 🚀 **Hazır Özellikler:**
- Vadeli hesap açma/kapatma
- Günlük bileşik faiz hesaplama
- Erken çekim penaltı sistemi
- Vade dolumu işlemleri
- Plan validasyon ve yönetimi

**Commit Hash**: `5cf61c8` - GitHub'a push edildi ✅

---

## 📋 Yapılacak İşler

### 3.1 RzwSavingsPlanService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsPlanService.cs`

**Görev**: Vadeli hesap planlarının yönetimi için service oluştur.

**Dependencies**:
- AppDbContext
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsPlanService>

**Metotlar**:
```csharp
Task<List<RzwSavingsPlan>> GetActivePlansAsync();
Task<RzwSavingsPlan?> GetPlanByIdAsync(int planId);
Task<RzwSavingsPlan?> GetPlanByTermTypeAsync(string termType);
Task<bool> ValidatePlanAsync(int planId, decimal amount);
Task InitializeDefaultPlansAsync();
```

**Default Planlar**:
- **Günlük**: %0.03/gün, Min: 100 RZW, Max: 1,000,000 RZW
- **Aylık**: %1.0/ay, Min: 500 RZW, Max: 5,000,000 RZW
- **Yıllık**: %15/yıl, Min: 1,000 RZW, Max: 10,000,000 RZW

### 3.2 RzwSavingsService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsService.cs`

**Görev**: Vadeli hesap işlemlerinin ana business logic'i.

**Dependencies**:
- AppDbContext
- RzwBalanceManagementService
- RzwSavingsPlanService
- RzwSavingsInterestService
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsService>

**Ana Metotlar**:
```csharp
// Vadeli hesap açma
Task<(bool Success, string Message, RzwSavingsAccount? Account)> CreateSavingsAccountAsync(
    int userId, int planId, decimal rzwAmount, bool autoRenew = false);

// Kullanıcının aktif vadeli hesapları
Task<List<RzwSavingsAccount>> GetUserActiveSavingsAsync(int userId);

// Vadeli hesap detayı
Task<RzwSavingsAccount?> GetSavingsAccountAsync(int accountId, int userId);

// Erken çekim
Task<(bool Success, string Message, decimal WithdrawnAmount)> EarlyWithdrawAsync(
    int accountId, int userId);

// Vade dolma işlemi
Task<(bool Success, string Message)> ProcessMaturityAsync(int accountId);

// Vadesi dolan hesapları getir
Task<List<RzwSavingsAccount>> GetMaturedAccountsAsync();

// Faiz ödemesi gereken hesapları getir
Task<List<RzwSavingsAccount>> GetAccountsForInterestPaymentAsync();
```

**İş Akışları**:
1. **Vadeli Hesap Açma**: Plan kontrolü → Bakiye kontrolü → RZW kilitleme → Hesap oluşturma
2. **Erken Çekim**: Hesap kontrolü → Ceza hesaplama → Faiz hesaplama → RZW serbest bırakma
3. **Vade Dolma**: Vade kontrolü → RZW serbest bırakma → Hesap kapatma

### 3.3 RzwSavingsInterestService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsInterestService.cs`

**Görev**: Faiz hesaplama ve ödeme işlemleri.

**Dependencies**:
- AppDbContext
- RzwBalanceManagementService
- RzwSavingsPlanService
- TradeService
- ITokenPriceService
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsInterestService>

**Ana Metotlar**:
```csharp
// Günlük faiz hesaplama ve ödeme
Task<(int ProcessedAccounts, decimal TotalInterestPaid)> ProcessDailyInterestAsync();

// Bileşik faiz hesaplama (günlük)
Task<decimal> CalculateDailyInterestAsync(RzwSavingsAccount account);

// Erken çekim faiz hesaplama
Task<decimal> CalculateEarlyWithdrawalInterestAsync(RzwSavingsAccount account);

// Faiz ödeme
Task<bool> PayInterestAsync(RzwSavingsAccount account, decimal interestAmount);

// Uygun plan bulma (erken çekim için)
Task<RzwSavingsPlan?> FindEligiblePlanForEarlyWithdrawalAsync(int heldDays);
```

**Faiz Hesaplama Kuralları**:
1. **Bileşik Faiz**: Günlük kapitalizasyon
2. **Günlük Oran**: Plan tipine göre yıllık oranın günlüğe bölümü
3. **Erken Çekim**: Tutulma süresine uygun daha düşük periyotlu plan faizi
4. **Uygun Plan Yoksa**: Hiç faiz ödenmez

**Formül**:
```
Günlük Faiz = (Ana Para + Kazanılan Faizler) × Günlük Oran
Günlük Oran = Yıllık Oran / 365 (veya plan tipine göre)
```

### 3.4 Service Registration
**Dosya**: `src/Program.cs`

**Görev**: Yeni servisleri dependency injection container'a ekle.

```csharp
// RZW Savings Services
builder.Services.AddScoped<RzwSavingsPlanService>();
builder.Services.AddScoped<RzwSavingsService>();
builder.Services.AddScoped<RzwSavingsInterestService>();
builder.Services.AddScoped<RzwBalanceManagementService>();
```

## ✅ Başarı Kriterleri

1. **Vadeli hesap açma**: Kullanıcı vadeli hesap açabiliyor
2. **Faiz hesaplama**: Bileşik faiz doğru hesaplanıyor
3. **Erken çekim**: Ceza ve faiz hesaplamaları doğru
4. **Vade dolma**: Otomatik vade dolma işlemi çalışıyor
5. **Balance operations**: RZW lock/unlock işlemleri doğru
6. **Trade records**: Tüm işlemlerde audit trail oluşuyor
7. **Default planlar**: Sistem başlangıcında planlar oluşuyor

## 🔧 Test Senaryoları

```csharp
// Test 1: Vadeli hesap açma
var result = await savingsService.CreateSavingsAccountAsync(userId: 1, planId: 1, rzwAmount: 1000m);
// Beklenen: Success = true, RZW kilitlendi, Trade kaydı oluştu

// Test 2: Günlük faiz hesaplama
var interest = await interestService.CalculateDailyInterestAsync(account);
// Beklenen: Bileşik faiz formülüne göre doğru hesaplama

// Test 3: Erken çekim
var withdrawal = await savingsService.EarlyWithdrawAsync(accountId: 1, userId: 1);
// Beklenen: Ceza düşüldü, uygun plan faizi ödendi, RZW serbest bırakıldı

// Test 4: Vade dolma
var maturity = await savingsService.ProcessMaturityAsync(accountId: 1);
// Beklenen: RZW serbest bırakıldı, hesap kapatıldı
```

## 📝 Önemli Notlar

- **Bileşik faiz**: Günlük kapitalizasyon ile hesapla
- **Erken çekim faizi**: Tutulma süresine göre uygun plan bul
- **Transaction safety**: Tüm işlemlerde database transaction kullan
- **Audit trail**: Her işlemde Trade kaydı oluştur
- **Error handling**: Kapsamlı hata kontrolü ve logging
- **Validation**: Tüm input'ları validate et

---

# Faz 4: Background Services - AI Agent Prompt

## 🎯 Görev Tanımı
RZW vadeli hesap sistemi için otomatik faiz ödemeleri ve vade kontrolleri yapan background service oluştur. Performanslı ve güvenilir batch processing implement et.

## 📋 Yapılacak İşler

### 4.1 RzwSavingsBackgroundService Oluştur
**Dosya**: `src/BackgroundServices/RzwSavingsBackgroundService.cs`

**Görev**: Günlük faiz ödemeleri ve vade dolma kontrollerini otomatik yapan background service.

**Base Class**: `BackgroundService`

**Dependencies**:
- IServiceProvider (scoped service'ler için)
- ILogger<RzwSavingsBackgroundService>
- IConfiguration (çalışma saatleri için)

**Ana Özellikler**:
```csharp
public class RzwSavingsBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RzwSavingsBackgroundService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromHours(1); // Her saat kontrol
    private readonly TimeSpan _processingTime = TimeSpan.FromHours(2); // Gece 02:00

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await WaitForProcessingTime(stoppingToken);
                await ProcessDailyOperationsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RZW savings background processing");
            }

            await Task.Delay(_processingInterval, stoppingToken);
        }
    }
}
```

**İşlem Akışı**:
1. **Günlük Faiz Ödemeleri**: Aktif vadeli hesaplar için faiz hesapla ve öde
2. **Vade Dolma Kontrolleri**: Vadesi dolan hesapları otomatik kapat
3. **Error Handling**: Hataları logla, işleme devam et
4. **Batch Processing**: Performans için toplu işlem yap

### 4.2 Background Service Metotları

**Ana İşlem Metodu**:
```csharp
private async Task ProcessDailyOperationsAsync()
{
    using var scope = _serviceProvider.CreateScope();
    var interestService = scope.ServiceProvider.GetRequiredService<RzwSavingsInterestService>();
    var savingsService = scope.ServiceProvider.GetRequiredService<RzwSavingsService>();

    // 1. Günlük faiz ödemeleri
    var (processedAccounts, totalInterest) = await interestService.ProcessDailyInterestAsync();
    _logger.LogInformation("Daily interest processed. Accounts: {Accounts}, Total: {Total}",
        processedAccounts, totalInterest);

    // 2. Vade dolma kontrolleri
    var maturedAccounts = await savingsService.GetMaturedAccountsAsync();
    var processedMaturity = 0;

    foreach (var account in maturedAccounts)
    {
        var result = await savingsService.ProcessMaturityAsync(account.Id);
        if (result.Success) processedMaturity++;
    }

    _logger.LogInformation("Maturity processed. Accounts: {Accounts}", processedMaturity);
}
```

**Zaman Kontrolü**:
```csharp
private async Task WaitForProcessingTime(CancellationToken cancellationToken)
{
    var now = DateTime.Now;
    var targetTime = now.Date.Add(_processingTime);

    // Eğer hedef saat geçmişse, yarın aynı saate ayarla
    if (now > targetTime)
        targetTime = targetTime.AddDays(1);

    var delay = targetTime - now;
    if (delay > TimeSpan.Zero)
    {
        _logger.LogInformation("Waiting until {TargetTime} for daily processing", targetTime);
        await Task.Delay(delay, cancellationToken);
    }
}
```

### 4.3 Service Registration
**Dosya**: `src/Program.cs`

**Görev**: Background service'i dependency injection container'a ekle.

```csharp
// Background Services
builder.Services.AddHostedService<RzwSavingsBackgroundService>();
```

### 4.4 Configuration Settings
**Dosya**: `src/appsettings.json`

**Görev**: Background service ayarları ekle.

```json
{
  "RzwSavings": {
    "BackgroundService": {
      "ProcessingHour": 2,
      "ProcessingIntervalMinutes": 60,
      "BatchSize": 100,
      "EnableProcessing": true
    }
  }
}
```

### 4.5 Error Handling ve Logging

**Hata Yönetimi**:
```csharp
private async Task<bool> ProcessAccountSafelyAsync(RzwSavingsAccount account,
    Func<RzwSavingsAccount, Task<bool>> processor)
{
    try
    {
        return await processor(account);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error processing account {AccountId} for user {UserId}",
            account.Id, account.UserId);
        return false;
    }
}
```

**Batch Processing**:
```csharp
private async Task ProcessAccountsBatchAsync<T>(IEnumerable<T> accounts,
    Func<T, Task<bool>> processor, int batchSize = 100)
{
    var batches = accounts.Chunk(batchSize);

    foreach (var batch in batches)
    {
        var tasks = batch.Select(account => ProcessAccountSafelyAsync(account, processor));
        await Task.WhenAll(tasks);

        // Batch'ler arası kısa bekleme
        await Task.Delay(TimeSpan.FromSeconds(1));
    }
}
```

## ✅ Başarı Kriterleri

1. **Background service çalışıyor**: Uygulama başladığında service aktif
2. **Günlük faiz ödemeleri**: Faizler otomatik hesaplanıp ödeniyor
3. **Vade dolma kontrolleri**: Vadesi dolan hesaplar otomatik kapatılıyor
4. **Error handling**: Hatalar loglanıyor, sistem durmuyor
5. **Performance**: Batch processing ile performanslı çalışıyor
6. **Logging**: Detaylı log kayıtları oluşuyor
7. **Configuration**: Ayarlar configuration'dan okunuyor

## 🔧 Test Senaryoları

```bash
# Test 1: Background service başlatma
dotnet run
# Beklenen: Service başlıyor, log'da "RzwSavingsBackgroundService started" mesajı

# Test 2: Manuel faiz ödeme testi
# Test database'de vadeli hesap oluştur, background service'in işlemesini bekle

# Test 3: Vade dolma testi
# Test database'de vadesi geçmiş hesap oluştur, otomatik kapatılmasını kontrol et

# Test 4: Error handling testi
# Hatalı veri ile test et, service'in durmamasını kontrol et
```

## 📝 Önemli Notlar

- **Gece saatleri**: Faiz ödemeleri gece 02:00'da yapılır
- **Scoped services**: Background service'de scoped service'ler için scope oluştur
- **Batch processing**: Performans için toplu işlem yap
- **Error isolation**: Bir hesaptaki hata diğerlerini etkilemez
- **Logging**: Detaylı log kayıtları tut
- **Configuration**: Ayarları configuration'dan oku
- **Graceful shutdown**: CancellationToken'ı doğru kullan

---

# Faz 5: User Interface - AI Agent Prompt

## 🎯 Görev Tanımı
RZW vadeli hesap sistemi için kullanıcı arayüzü oluştur. Mevcut Wallet sayfasını güncelle ve yeni RZW Savings sayfaları ekle. Responsive ve kullanıcı dostu tasarım yap.

## 📋 Yapılacak İşler

### 5.1 Wallet Sayfası Güncellemeleri

#### 5.1.1 Wallet ViewModel Güncelleme
**Dosya**: `src/Areas/Admin/ViewModels/Wallet/WalletViewModel.cs`

**Görev**: Mevcut WalletViewModel'i RZW için özel alanlarla genişlet.

```csharp
public class WalletViewModel
{
    // Mevcut alanlar...
    public decimal Balance { get; set; } // Available Balance

    // YENİ ALANLAR
    public decimal LockedBalance { get; set; }
    public decimal TotalBalance => Balance + LockedBalance;

    // RZW özel alanlar
    public bool IsRzwToken { get; set; }
    public decimal? LockedInSavings { get; set; }
    public int? ActiveSavingsCount { get; set; }
    public List<SavingsLockInfo>? SavingsLocks { get; set; }
}
```

#### 5.1.2 Wallet Controller Güncelleme
**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml.cs`

**Görev**: Wallet page model'ini yeni balance bilgileri ile güncelle.

```csharp
public class WalletModel : PageModel
{
    private readonly IWalletService _walletService;
    private readonly RzwBalanceManagementService _rzwBalanceService;
    private readonly ITokenPriceService _tokenPriceService;

    public List<WalletBalanceInfo> WalletBalances { get; set; } = new();
    public RzwBalanceInfo? RzwBalance { get; set; }

    public async Task OnGetAsync()
    {
        var userId = GetCurrentUserId();
        WalletBalances = await _walletService.GetUserAllBalanceInfoAsync(userId);

        var rzwTokenId = await _tokenPriceService.GetRzwTokenIdAsync();
        var rzwWallet = WalletBalances.FirstOrDefault(w => w.CoinId == rzwTokenId);

        if (rzwWallet != null)
        {
            RzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId);
        }
    }
}
```

#### 5.1.3 Wallet View Güncelleme
**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml`

**Görev**: Wallet sayfasında RZW için özel kart gösterimi ekle.

**RZW Özel Kart**:
```html
@if (Model.RzwBalance != null)
{
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card rzw-wallet-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-coins me-2"></i>
                    RZW Token
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">@Localizer["Available"]</small>
                        <div class="h6 text-success">@Model.RzwBalance.AvailableRzw.ToString("N8", true)</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">@Localizer["Locked"]</small>
                        <div class="h6 text-warning">@Model.RzwBalance.LockedRzw.ToString("N8", true)</div>
                    </div>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <span class="text-muted">@Localizer["Total"]:</span>
                    <strong>@Model.RzwBalance.TotalRzw.ToString("N8", true) RZW</strong>
                </div>

                @if (Model.RzwBalance.ActiveSavingsCount > 0)
                {
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-piggy-bank me-1"></i>
                            @Localizer["Active Savings"]: @Model.RzwBalance.ActiveSavingsCount
                        </small>
                    </div>
                }

                <div class="mt-3">
                    <a href="/MyAccount/RzwSavings" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        @Localizer["Create Savings Account"]
                    </a>
                </div>
            </div>
        </div>
    </div>
}
```

### 5.2 RZW Savings Sayfaları

#### 5.2.1 RZW Savings Index Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Index.cshtml`

**Görev**: Kullanıcının vadeli hesaplarını listeleyen ana sayfa.

**Özellikler**:
- Aktif vadeli hesaplar listesi
- Toplam kilitli RZW miktarı
- Toplam kazanılan faiz
- Yeni vadeli hesap oluşturma linki

#### 5.2.2 RZW Savings Create Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Create.cshtml`

**Görev**: Yeni vadeli hesap oluşturma formu.

**Form Alanları**:
```html
<form method="post">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="PlanId">@Localizer["Savings Plan"]</label>
                <select asp-for="PlanId" class="form-control" required>
                    <option value="">@Localizer["Select Plan"]</option>
                    @foreach (var plan in Model.AvailablePlans)
                    {
                        <option value="@plan.Id"
                                data-rate="@plan.InterestRate"
                                data-min="@plan.MinRzwAmount"
                                data-max="@plan.MaxRzwAmount">
                            @plan.Name - @plan.InterestRate.ToString("P2")
                            (Min: @plan.MinRzwAmount.ToString("N0") RZW)
                        </option>
                    }
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="RzwAmount">@Localizer["RZW Amount"]</label>
                <input asp-for="RzwAmount" type="number" step="0.********"
                       class="form-control" required>
                <small class="text-muted">
                    @Localizer["Available"]: @Model.AvailableRzw.ToString("N8", true) RZW
                </small>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="form-check">
            <input asp-for="AutoRenew" type="checkbox" class="form-check-input">
            <label asp-for="AutoRenew" class="form-check-label">
                @Localizer["Auto Renew"]
            </label>
        </div>
    </div>

    <div class="form-group">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            @Localizer["Create Savings Account"]
        </button>
        <a href="/MyAccount/RzwSavings" class="btn btn-secondary">
            @Localizer["Cancel"]
        </a>
    </div>
</form>
```

#### 5.2.3 RZW Savings Detail Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Detail.cshtml`

**Görev**: Vadeli hesap detayları ve işlem geçmişi.

**Özellikler**:
- Hesap bilgileri (tutar, vade, faiz oranı)
- Kazanılan faiz geçmişi
- Erken çekim seçeneği
- Vade dolma tarihi countdown

### 5.3 ViewModels

#### 5.3.1 RzwSavings ViewModels
**Dosya**: `src/Areas/Admin/ViewModels/RzwSavings/`

**CreateRzwSavingsViewModel.cs**:
```csharp
public class CreateRzwSavingsViewModel
{
    [Required]
    [Display(Name = "Savings Plan")]
    public int PlanId { get; set; }

    [Required]
    [Range(0.********, double.MaxValue)]
    [Display(Name = "RZW Amount")]
    public decimal RzwAmount { get; set; }

    [Display(Name = "Auto Renew")]
    public bool AutoRenew { get; set; }

    // Helper properties
    public List<RzwSavingsPlan> AvailablePlans { get; set; } = new();
    public decimal AvailableRzw { get; set; }
}
```

**RzwSavingsIndexViewModel.cs**:
```csharp
public class RzwSavingsIndexViewModel
{
    public List<RzwSavingsAccount> ActiveSavings { get; set; } = new();
    public decimal TotalLockedRzw { get; set; }
    public decimal TotalEarnedInterest { get; set; }
    public decimal AvailableRzw { get; set; }
    public int ActiveSavingsCount { get; set; }
}
```

### 5.4 JavaScript Enhancements

#### 5.4.1 RZW Savings JavaScript
**Dosya**: `src/wwwroot/js/rzw-savings.js`

**Görev**: Form validasyonu ve kullanıcı deneyimi iyileştirmeleri.

```javascript
// Plan seçimi değiştiğinde min/max değerleri güncelle
$('#PlanId').on('change', function() {
    const selectedOption = $(this).find('option:selected');
    const minAmount = selectedOption.data('min');
    const maxAmount = selectedOption.data('max');
    const rate = selectedOption.data('rate');

    $('#RzwAmount').attr('min', minAmount);
    if (maxAmount) {
        $('#RzwAmount').attr('max', maxAmount);
    }

    updateEstimatedEarnings();
});

// Tahmini kazanç hesaplama
function updateEstimatedEarnings() {
    const amount = parseFloat($('#RzwAmount').val()) || 0;
    const rate = parseFloat($('#PlanId option:selected').data('rate')) || 0;

    if (amount > 0 && rate > 0) {
        const dailyEarnings = amount * (rate / 365);
        $('#estimated-earnings').text(dailyEarnings.toFixed(8) + ' RZW/day');
    }
}

// Countdown timer for maturity date
function startCountdown(maturityDate, elementId) {
    const countdownElement = document.getElementById(elementId);

    function updateCountdown() {
        const now = new Date().getTime();
        const maturity = new Date(maturityDate).getTime();
        const distance = maturity - now;

        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

            countdownElement.innerHTML = `${days}d ${hours}h ${minutes}m`;
        } else {
            countdownElement.innerHTML = window.t["Matured"];
        }
    }

    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
}
```

### 5.5 Localization Keys

**Dosya**: `src/Areas/Admin/Resources/SharedResource.tr.resx`

**Görev**: RZW Savings için gerekli dil anahtarları ekle.

```xml
<data name="RZW Savings" xml:space="preserve">
    <value>RZW Vadeli Hesap</value>
</data>
<data name="Create Savings Account" xml:space="preserve">
    <value>Vadeli Hesap Oluştur</value>
</data>
<data name="Savings Plan" xml:space="preserve">
    <value>Vadeli Plan</value>
</data>
<data name="Available" xml:space="preserve">
    <value>Kullanılabilir</value>
</data>
<data name="Locked" xml:space="preserve">
    <value>Kilitli</value>
</data>
<data name="Active Savings" xml:space="preserve">
    <value>Aktif Vadeli Hesap</value>
</data>
<data name="Early Withdrawal" xml:space="preserve">
    <value>Erken Çekim</value>
</data>
<data name="Maturity Date" xml:space="preserve">
    <value>Vade Tarihi</value>
</data>
<data name="Interest Rate" xml:space="preserve">
    <value>Faiz Oranı</value>
</data>
<data name="Total Earned" xml:space="preserve">
    <value>Toplam Kazanç</value>
</data>
```

## ✅ Başarı Kriterleri

1. **Wallet sayfası**: RZW için özel kart gösterimi çalışıyor
2. **Vadeli hesap listesi**: Kullanıcının aktif hesapları listeleniyor
3. **Yeni hesap oluşturma**: Form validasyonu ile hesap oluşturuluyor
4. **Hesap detayları**: Detay sayfası ve işlem geçmişi görüntüleniyor
5. **Responsive design**: Mobil uyumlu tasarım
6. **JavaScript**: Form validasyonu ve UX iyileştirmeleri çalışıyor
7. **Localization**: Tüm metinler çevrilmiş

## 🔧 Test Senaryoları

```bash
# Test 1: Wallet sayfası
# /MyAccount/Wallet sayfasına git
# RZW kartının özel gösterimini kontrol et

# Test 2: Vadeli hesap oluşturma
# /MyAccount/RzwSavings/Create sayfasına git
# Form validasyonunu test et
# Yeni hesap oluştur

# Test 3: Hesap listesi
# /MyAccount/RzwSavings sayfasına git
# Aktif hesapların listelendiğini kontrol et

# Test 4: Responsive design
# Mobil cihazda sayfaları test et
```

## 📝 Önemli Notlar

- **Mevcut tasarımı koru**: Proje tasarım standartlarını takip et
- **Responsive**: Bootstrap kullanarak mobil uyumlu yap
- **Validation**: Client-side ve server-side validasyon ekle
- **UX**: Kullanıcı dostu arayüz tasarla
- **Performance**: Lazy loading ve pagination kullan
- **Security**: XSS ve CSRF koruması ekle
- **Accessibility**: ARIA etiketleri ve keyboard navigation

---

# Faz 6: Admin Panel & Testing - AI Agent Prompt

## 🎯 Görev Tanımı
RZW vadeli hesap sistemi için admin panel oluştur ve kapsamlı testler yap. Sistem yönetimi, raporlama ve monitoring özellikleri ekle.

## 📋 Yapılacak İşler

### 6.1 Admin Panel - Vadeli Hesap Planları Yönetimi

#### 6.1.1 RzwSavingsPlans Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsPlans/Index.cshtml.cs`

**Görev**: Vadeli hesap planlarının CRUD işlemleri için admin sayfası.

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsPlanService _planService;

    public List<RzwSavingsPlan> Plans { get; set; } = new();

    public async Task OnGetAsync()
    {
        Plans = await _planService.GetAllPlansAsync();
    }

    public async Task<IActionResult> OnPostToggleStatusAsync(int id)
    {
        await _planService.ToggleStatusAsync(id);
        return RedirectToPage();
    }
}
```

#### 6.1.2 Plan Yönetimi Sayfaları
**Dosyalar**:
- `src/Areas/Admin/Pages/RzwSavingsPlans/Index.cshtml`
- `src/Areas/Admin/Pages/RzwSavingsPlans/Create.cshtml`
- `src/Areas/Admin/Pages/RzwSavingsPlans/Edit.cshtml`

**Özellikler**:
- Plan listesi (aktif/pasif durumu)
- Yeni plan oluşturma
- Mevcut plan düzenleme
- Plan aktif/pasif yapma
- Plan kullanım istatistikleri

### 6.2 Admin Panel - Kullanıcı Vadeli Hesapları

#### 6.2.1 RzwSavingsAccounts Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsAccounts/Index.cshtml.cs`

**Görev**: Tüm kullanıcıların vadeli hesaplarını yönetme.

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsService _savingsService;

    public List<RzwSavingsAccountViewModel> Accounts { get; set; } = new();
    public PaginationInfo Pagination { get; set; } = new();

    [BindProperty(SupportsGet = true)]
    public string? SearchEmail { get; set; }

    [BindProperty(SupportsGet = true)]
    public string? Status { get; set; }

    public async Task OnGetAsync(int pageNumber = 1, int pageSize = 50)
    {
        var result = await _savingsService.GetAccountsForAdminAsync(
            searchEmail: SearchEmail,
            status: Status,
            pageNumber: pageNumber,
            pageSize: pageSize);

        Accounts = result.Accounts;
        Pagination = result.Pagination;
    }
}
```

#### 6.2.2 Hesap Yönetimi Özellikleri
**Özellikler**:
- Tüm vadeli hesapları listeleme
- Kullanıcı email'i ile arama
- Durum filtreleme (Active, Matured, Withdrawn)
- Hesap detayları görüntüleme
- Manuel vade dolma işlemi
- Acil durum hesap kapatma

### 6.3 Admin Panel - Raporlama

#### 6.3.1 RzwSavingsReports Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsReports/Index.cshtml.cs`

**Görev**: Vadeli hesap sistemi raporları.

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsReportService _reportService;

    public RzwSavingsReportViewModel Report { get; set; } = new();

    public async Task OnGetAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        Report = await _reportService.GenerateReportAsync(start, end);
    }
}
```

#### 6.3.2 Rapor Türleri
**Raporlar**:
- **Genel İstatistikler**: Toplam kilitli RZW, aktif hesap sayısı, toplam faiz
- **Plan Performansı**: Plan bazında kullanım oranları
- **Kullanıcı İstatistikleri**: En aktif kullanıcılar, ortalama hesap büyüklüğü
- **Faiz Ödemeleri**: Günlük/aylık faiz ödeme raporları
- **Erken Çekim Analizi**: Erken çekim oranları ve ceza gelirleri

### 6.4 RzwSavingsReportService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsReportService.cs`

**Görev**: Raporlama için özel service.

```csharp
public class RzwSavingsReportService
{
    private readonly AppDbContext _context;

    public async Task<RzwSavingsReportViewModel> GenerateReportAsync(DateTime startDate, DateTime endDate)
    {
        var report = new RzwSavingsReportViewModel
        {
            StartDate = startDate,
            EndDate = endDate,

            // Genel istatistikler
            TotalActiveAccounts = await GetActiveAccountsCountAsync(),
            TotalLockedRzw = await GetTotalLockedRzwAsync(),
            TotalInterestPaid = await GetTotalInterestPaidAsync(startDate, endDate),

            // Plan istatistikleri
            PlanStatistics = await GetPlanStatisticsAsync(),

            // Günlük faiz ödemeleri
            DailyInterestPayments = await GetDailyInterestPaymentsAsync(startDate, endDate),

            // Erken çekim istatistikleri
            EarlyWithdrawalStats = await GetEarlyWithdrawalStatsAsync(startDate, endDate)
        };

        return report;
    }
}
```

### 6.5 System Monitoring

#### 6.5.1 RzwSavingsMonitoringService
**Dosya**: `src/Areas/Admin/Services/RzwSavingsMonitoringService.cs`

**Görev**: Sistem sağlığı ve performans monitoring.

```csharp
public class RzwSavingsMonitoringService
{
    public async Task<SystemHealthReport> GetSystemHealthAsync()
    {
        return new SystemHealthReport
        {
            BackgroundServiceStatus = await CheckBackgroundServiceStatusAsync(),
            PendingInterestPayments = await GetPendingInterestPaymentsCountAsync(),
            OverdueMaturityAccounts = await GetOverdueMaturityAccountsCountAsync(),
            SystemErrors = await GetRecentSystemErrorsAsync(),
            PerformanceMetrics = await GetPerformanceMetricsAsync()
        };
    }
}
```

### 6.6 Comprehensive Testing

#### 6.6.1 Unit Tests
**Dosya**: `src_unittests/RazeWinComTr.RzwSavingsTests/`

**Test Kategorileri**:

**InterestCalculationTests.cs**:
```csharp
[TestClass]
public class InterestCalculationTests
{
    [TestMethod]
    public void CalculateDailyInterest_WithCompoundInterest_ReturnsCorrectAmount()
    {
        // Arrange
        var account = new RzwSavingsAccount
        {
            RzwAmount = 1000m,
            TotalEarnedRzw = 50m, // Önceden kazanılan faiz
            InterestRate = 0.0003m // %0.03 günlük
        };

        // Act
        var dailyInterest = CalculateDailyInterest(account);

        // Assert
        var expectedInterest = (1000m + 50m) * 0.0003m; // Bileşik faiz
        Assert.AreEqual(expectedInterest, dailyInterest);
    }

    [TestMethod]
    public void CalculateEarlyWithdrawalInterest_WithEligiblePlan_ReturnsCorrectAmount()
    {
        // Test erken çekim faiz hesaplama
    }
}
```

**BalanceManagementTests.cs**:
```csharp
[TestClass]
public class BalanceManagementTests
{
    [TestMethod]
    public async Task LockRzwForSavings_WithSufficientBalance_SuccessfullyLocks()
    {
        // Test RZW kilitleme işlemi
    }

    [TestMethod]
    public async Task UnlockRzwFromSavings_WithValidAccount_SuccessfullyUnlocks()
    {
        // Test RZW serbest bırakma işlemi
    }
}
```

#### 6.6.2 Integration Tests
**Dosya**: `src_unittests/RazeWinComTr.RzwSavingsTests/Integration/`

**SavingsAccountFlowTests.cs**:
```csharp
[TestClass]
public class SavingsAccountFlowTests
{
    [TestMethod]
    public async Task CreateSavingsAccount_FullFlow_CompletesSuccessfully()
    {
        // End-to-end vadeli hesap açma testi
        // 1. Plan seç
        // 2. Bakiye kontrol et
        // 3. Hesap oluştur
        // 4. RZW kilitle
        // 5. Trade kaydı kontrol et
    }

    [TestMethod]
    public async Task EarlyWithdrawal_FullFlow_CompletesWithPenalty()
    {
        // End-to-end erken çekim testi
    }
}
```

#### 6.6.3 Performance Tests
**Dosya**: `src_unittests/RazeWinComTr.RzwSavingsTests/Performance/`

**BackgroundServicePerformanceTests.cs**:
```csharp
[TestClass]
public class BackgroundServicePerformanceTests
{
    [TestMethod]
    public async Task ProcessDailyInterest_With1000Accounts_CompletesInReasonableTime()
    {
        // 1000 hesap ile performans testi
        var stopwatch = Stopwatch.StartNew();

        await backgroundService.ProcessDailyInterestAsync();

        stopwatch.Stop();
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 30000); // 30 saniyeden az
    }
}
```

### 6.7 Documentation

#### 6.7.1 API Documentation
**Dosya**: `docs/rzw-saving-feature/API_DOCUMENTATION.md`

**İçerik**:
- Service metotları ve parametreleri
- Response formatları
- Error codes ve handling
- Usage examples

#### 6.7.2 User Guide
**Dosya**: `docs/rzw-saving-feature/USER_GUIDE.md`

**İçerik**:
- Vadeli hesap nasıl açılır
- Faiz hesaplama kuralları
- Erken çekim koşulları
- SSS (Sık Sorulan Sorular)

#### 6.7.3 Admin Guide
**Dosya**: `docs/rzw-saving-feature/ADMIN_GUIDE.md`

**İçerik**:
- Plan yönetimi
- Kullanıcı hesap yönetimi
- Raporlama
- Troubleshooting

## ✅ Başarı Kriterleri

1. **Admin panel çalışıyor**: Tüm admin sayfaları erişilebilir
2. **Plan yönetimi**: CRUD işlemleri çalışıyor
3. **Hesap yönetimi**: Kullanıcı hesapları yönetilebiliyor
4. **Raporlama**: Detaylı raporlar oluşturuluyor
5. **Monitoring**: Sistem sağlığı izlenebiliyor
6. **Unit tests**: %90+ code coverage
7. **Integration tests**: Ana akışlar test ediliyor
8. **Performance tests**: Kabul edilebilir performans
9. **Documentation**: Kapsamlı dokümantasyon

## 🔧 Test Komutları

```bash
# Unit tests çalıştır
dotnet test src_unittests/RazeWinComTr.RzwSavingsTests/

# Coverage raporu oluştur
dotnet test --collect:"XPlat Code Coverage"

# Performance tests
dotnet test --filter Category=Performance

# Integration tests
dotnet test --filter Category=Integration

# Build ve deployment kontrolü
dotnet build --configuration Release
```

## 📝 Önemli Notlar

- **Security**: Admin panel için yetkilendirme kontrolleri
- **Performance**: Büyük veri setleri için pagination
- **Monitoring**: Sistem sağlığı için alerting
- **Backup**: Kritik işlemler öncesi backup
- **Logging**: Detaylı audit trail
- **Testing**: Comprehensive test coverage
- **Documentation**: Güncel ve kapsamlı dokümantasyon

---

## 📊 **GENEL IMPLEMENTATION DURUMU**

### ✅ **Tamamlanan Fazlar:**
- ✅ **Faz 1: Database & Models** - TAMAMLANDI
- ✅ **Faz 2: Balance Management** - TAMAMLANDI
- ✅ **Faz 3: Savings System Core** - TAMAMLANDI ✅

### 🔄 **Devam Eden Fazlar:**
- ⏳ **Faz 4: Background Services** - HAZIR
- ⏳ **Faz 5: User Interface** - HAZIR
- ⏳ **Faz 6: Admin Panel & Testing** - HAZIR

### 📈 **İlerleme Durumu:**
- **Tamamlanan**: 3/6 faz (%50)
- **Core Business Logic**: %100 tamamlandı
- **Database Layer**: %100 tamamlandı
- **Service Layer**: %100 tamamlandı
- **Unit Tests**: 26/26 test geçiyor (%100)

### 🎯 **Sonraki Adım:**
**Faz 4: Background Services** implementasyonu için hazır!

### 📁 **GitHub Durumu:**
- **Branch**: `feature/rzw-vadeli-hesap-faz1-database-models`
- **Son Commit**: `5cf61c8` - "feat: RZW Vadeli Hesap Faz 3 - Core Business Logic ve Servisler"
- **Push Durumu**: ✅ Başarıyla push edildi

---

**Proje İlerlemesi**: %50 Tamamlandı
**Toplam Tahmini Süre**: 11-18 gün (3 faz tamamlandı)
**Tüm Fazlar**: Bağımsız olarak test edilebilir ve deploy edilebilir
