# RZW Vadeli Hesap Sistemi - Ana Plan Dokümantasyonu

## 📋 Proje Özeti

RazeWin platformunda sadece RZW Token için vadeli hesap sistemi geliştirilmesi. Kullanıcılar RZW tokenlarını günlük, a<PERSON><PERSON><PERSON> veya yıllık vadelerle kilitleyerek garantili faiz kazanabilecekler.

## 🎯 Temel Gereksinimler

### Fonksiyonel Gereksinimler
- ✅ Sadece RZW Token için vadeli hesap
- ✅ Gü<PERSON>lük, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> vade seçenekleri
- ✅ Kullanılabilir/Kilitli/Toplam bakiye ayrımı
- ✅ Otomatik faiz hesaplama ve dağıtım
- ✅ Erken çekim (ceza ile)
- ✅ Admin panel yönetimi
- ✅ Kullanıcı arayüzü

### Teknik Gereksinimler
- ✅ Mevcut BALANCE kolonu = Available Balance
- ✅ Yeni LOCKED_BALANCE kolonu ekleme
- ✅ TOTAL_BALANCE hesaplanmış alan (DB'de tutulmayacak)
- ✅ Metot isimlerinde available/locked/total terimleri
- ✅ Background service ile otomatik faiz
- ✅ Transaction tracking

## 🏗️ Sistem Mimarisi

### Database Schema
```
WALLET Tablosu:
- BALANCE (mevcut) -> Available Balance
- LOCKED_BALANCE (yeni) -> Kilitli Balance
- TotalBalance = Balance + LockedBalance (hesaplanmış)

RZW_SAVINGS_ACCOUNT Tablosu:
- Vadeli hesap bilgileri

RZW_SAVINGS_INTEREST_PAYMENT Tablosu:
- Faiz ödeme kayıtları

RZW_SAVINGS_PLAN Tablosu:
- Vadeli hesap planları
```

### Service Katmanı
```
RzwBalanceManagementService:
- Bakiye kilitleme/serbest bırakma
- Bakiye bilgilerini getirme
- TokenPriceService dependency ile dinamik RZW token ID

RzwSavingsService:
- Vadeli hesap CRUD işlemleri
- Erken çekim hesaplamaları

RzwSavingsInterestService:
- Faiz hesaplama ve dağıtım

RzwSavingsBackgroundService:
- Günlük faiz ödemeleri
- Vade kontrolleri

TokenPriceService (mevcut):
- RZW token ID ve fiyat bilgileri
- Dinamik token bilgisi sağlama

TradeService (mevcut):
- Wallet balance değişikliklerini TRADE tablosuna kaydetme
- Coin-specific transaction tracking
- Balance lock/unlock için yeni TradeType'lar
```

## 📊 Faiz Oranları (Bileşik Faiz Sistemi)

### RZW Vadeli Hesap Planları
- **Günlük**: %0.03/gün (Yıllık ~%11) - Min: 100 RZW
- **Aylık**: %1.0/ay (Yıllık ~%12) - Min: 500 RZW
- **Yıllık**: %15/yıl - Min: 1,000 RZW

### Bileşik Faiz Hesaplama
- **Günlük Kapitalizasyon**: Faizler her gün ana paraya eklenir
- **Formül**: `(başlangıç_tutarı + kazanılan_faizler) * günlük_oran`
- **Günlük Oran**: Plan tipine göre yıllık oranın günlüğe bölümü

### Erken Çekim Faiz Politikası
- **Tutulma Süresi Hesabı**: Çekim tarihi - başlangıç tarihi
- **Uygun Plan Arama**: Tutulma süresinden kısa/eşit periyotlu planlar
- **Plan Seçimi**: En uzun süreli plan (en yüksek faiz oranı)
- **Faiz Hesaplama**: Uygun planın oranı ile bileşik faiz
- **Uygun Plan Yoksa**: Hiç faiz ödenmez

### Örnek Senaryolar
1. **1 günlük → erken çekim**: Daha düşük plan yok → Faiz = 0
2. **30 günlük → 15 gün tutup çekim**: 1 günlük plan varsa → 1 günlük oran × 15 gün
3. **365 günlük → 45 gün tutup çekim**: 30 günlük plan varsa → 30 günlük oran × 45 gün

## 🔄 İş Akışları

### Vadeli Hesap Açma
1. Kullanıcı plan seçer
2. RZW miktarı girer
3. Available balance kontrolü
4. Balance -> LockedBalance transfer
5. RzwSavingsAccount kaydı
6. Transaction kaydı

### Faiz Ödeme (Günlük)
1. Background service çalışır
2. Aktif vadeli hesapları bulur
3. Faiz hesaplar
4. Available balance'a ekler
5. Interest payment kaydı
6. Transaction kaydı

### Vade Dolma/Erken Çekim
1. Vadeli hesap kontrolü
2. Ceza hesaplama (erken çekim)
3. LockedBalance -> Available transfer
4. Vadeli hesap kapatma
5. Transaction kaydı

## 📁 Dosya Yapısı

```
src/
├── Areas/Admin/
│   ├── DbModel/
│   │   ├── RzwSavingsAccount.cs
│   │   ├── RzwSavingsInterestPayment.cs
│   │   ├── RzwSavingsPlan.cs
│   │   └── RzwSavingsEnums.cs
│   ├── Services/
│   │   ├── RzwBalanceManagementService.cs (TokenPriceService dependency)
│   │   ├── RzwSavingsService.cs
│   │   ├── RzwSavingsInterestService.cs (TradeService dependency)
│   │   ├── TradeService.cs (mevcut - değişiklik yok)
│   │   └── TokenPriceService.cs (mevcut - değişiklik yok)
│   ├── ViewModels/
│   │   ├── RzwSavings/
│   │   └── Wallet/ (güncellenecek)
│   └── Pages/
│       └── RzwSavings/
├── Areas/MyAccount/Pages/
│   ├── RzwSavings/
│   └── Wallet.cshtml (güncellenecek)
├── BackgroundServices/
│   └── RzwSavingsBackgroundService.cs
├── Models/
│   ├── WalletBalanceInfo.cs
│   └── RzwBalanceInfo.cs
└── Migrations/
    └── AddLockedBalanceToWallet.cs
```

## 🎨 UI Tasarımı

### Wallet Sayfası
- RZW için özel kart (Available/Locked/Total)
- Diğer coinler için basit gösterim
- Vadeli hesap linkı

### RZW Savings Sayfası
- Aktif vadeli hesaplar listesi
- Yeni vadeli hesap formu
- Faiz geçmişi
- Erken çekim seçeneği

### Admin Panel
- Vadeli hesap planları yönetimi
- Kullanıcı vadeli hesapları
- Faiz ödemeleri raporu

## 🔒 Güvenlik

### Bakiye Kontrolleri
- Available balance yeterlilik kontrolü
- Locked balance doğrulama
- Transaction atomicity
- **Audit trail**: Tüm wallet değişiklikleri TRADE tablosuna kaydedilir
- **Coin-specific tracking**: Her coin için ayrı Trade kayıtları
- **Balance lock/unlock tracking**: Kilitleme işlemleri için yeni TradeType enum değerleri
- **Reference tracking**: RzwSavingsAccountId ile savings account'a referans

### Erken Çekim Cezaları
- %10 ceza oranı
- Minimum tutma süresi
- Ceza hesaplama şeffaflığı

## 📈 Performans

### Database Optimizasyonu
- LOCKED_BALANCE index
- Maturity date index
- User-coin composite index

### Background Service
- Gece saatleri çalışma
- Batch processing
- Error handling

## 🧪 Test Stratejisi

### Unit Tests
- Faiz hesaplama algoritmaları
- Bakiye kilitleme/serbest bırakma
- Erken çekim hesaplamaları

### Integration Tests
- Vadeli hesap açma akışı
- Faiz ödeme süreci
- Vade dolma işlemleri

### UI Tests
- Bakiye gösterimi
- Form validasyonları
- Admin panel işlemleri

## 📋 Yapılacaklar Listesi

Bu plan 6 ana faza bölünmüştür. Detaylar ayrı dosyalarda:

1. **Faz 1**: Database & Models → `RZW_SAVINGS_PHASE_1.md`
2. **Faz 2**: Balance Management → `RZW_SAVINGS_PHASE_2.md`
3. **Faz 3**: Savings System → `RZW_SAVINGS_PHASE_3.md`
4. **Faz 4**: Background Services → `RZW_SAVINGS_PHASE_4.md`
5. **Faz 5**: User Interface → `RZW_SAVINGS_PHASE_5.md`
6. **Faz 6**: Admin Panel & Testing → `RZW_SAVINGS_PHASE_6.md`

## 📞 İletişim ve Notlar

### Önemli Kararlar
- BALANCE kolonu Available Balance olarak kullanılacak
- TOTAL_BALANCE DB'de tutulmayacak (hesaplanmış alan)
- Sadece RZW Token desteklenecek
- Metot isimleri available/locked/total içerecek
- RZW Token ID dinamik olarak TokenPriceService'den alınacak (sabit değer kullanılmayacak)
- Mevcut TokenPriceService kullanılacak, yeni service dependency eklenmeyecek
- **KRİTİK**: Tüm wallet değişikliklerinde TRADE tablosuna kayıt yapılacak (TradeService)
- Balance lock/unlock işlemleri için yeni TradeType enum değerleri kullanılacak
- TRADE tablosuna RzwSavingsAccountId field eklenerek referans sağlanacak
- **BİLEŞİK FAİZ**: Günlük kapitalizasyon ile bileşik faiz hesaplanacak
- **ERKEN ÇEKİM**: Tutulma süresine uygun daha düşük periyotlu plan faizi uygulanacak
- **UYGUN PLAN YOKSA**: Erken çekimde hiç faiz ödenmeyecek

### Gelecek Geliştirmeler
- Compound interest seçeneği
- Otomatik yenileme
- Mobil uygulama entegrasyonu
- Diğer tokenlar için destek

---
**Son Güncelleme**: 2024-12-19 UTC
**Versiyon**: 1.0
**Durum**: Planlama Tamamlandı
