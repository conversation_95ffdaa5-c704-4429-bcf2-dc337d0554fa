# RZW Vadeli Hesap Sistemi - Uygulama Başlatma Prompt'u

## 🎯 Ana Görev
RazeWin platformuna RZW Token için vadeli hesap sistemi ekle. Sistem 6 faza b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lu<PERSON>, her faz artırımlı olarak geliştirilecek ve test edilecek.

## 📋 Proje <PERSON>
- **Hedef**: Sadece RZW Token için vadeli hesap sistemi
- **Vade Seçenekleri**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%0.03), <PERSON><PERSON><PERSON><PERSON> (%1.0), <PERSON><PERSON><PERSON><PERSON><PERSON> (%15)
- **Özellikler**: Bileşik faiz, er<PERSON> (ceza ile), otomatik faiz ödemeleri
- **Kısıt**: Mevcut sistemi etkilememe, backward compatibility

## 🗂️ İmplementasyon Dokümantasyonu

### Ana Planlama Dokümantasyonu
1. **`docs/rzw-saving-feature/RZW_SAVINGS_MASTER_PLAN.md`** - Genel sistem tasarımı ve iş kuralları
2. **`docs/rzw-saving-feature/RZW_SAVINGS_IMPLEMENTATION_PLAN.md`** - Detaylı uygulama planı
3. **`docs/rzw-saving-feature/prompts/README.md`** - Faz kılavuzu

### Faz Bazında Prompt Dosyaları
1. **`docs/rzw-saving-feature/prompts/PHASE_1_DATABASE_MODELS.prompt`**
2. **`docs/rzw-saving-feature/prompts/PHASE_2_BALANCE_MANAGEMENT.prompt`**
3. **`docs/rzw-saving-feature/prompts/PHASE_3_SAVINGS_SYSTEM_CORE.prompt`**
4. **`docs/rzw-saving-feature/prompts/PHASE_4_BACKGROUND_SERVICES.prompt`**
5. **`docs/rzw-saving-feature/prompts/PHASE_5_USER_INTERFACE.prompt`**
6. **`docs/rzw-saving-feature/prompts/PHASE_6_ADMIN_PANEL_TESTING.prompt`**

## 🚀 Başlatma Talimatları

### Adım 1: Dokümantasyonu İncele
Öncelikle şu dosyaları oku ve anla:
```
1. docs/rzw-saving-feature/RZW_SAVINGS_MASTER_PLAN.md
2. docs/rzw-saving-feature/RZW_SAVINGS_IMPLEMENTATION_PLAN.md
3. docs/rzw-saving-feature/prompts/README.md
```

### Adım 2: Mevcut Sistemi Analiz Et
Şu dosyaları incele ve mevcut yapıyı anla:
```
- src/Areas/Admin/DbModel/Wallet.cs
- src/Areas/Admin/DbModel/Trade.cs
- src/Areas/Admin/Services/WalletService.cs
- src/Areas/Admin/Services/TokenPriceService.cs
- src/Areas/Admin/DbModel/AppDbContext.cs
```

### Adım 3: Faz 1'i Başlat
**`docs/rzw-saving-feature/prompts/PHASE_1_DATABASE_MODELS.prompt`** dosyasındaki talimatları uygula.

**Faz 1 Görevleri**:
- Wallet entity'sine LOCKED_BALANCE kolonu ekle
- RZW Savings entity'leri oluştur (RzwSavingsAccount, RzwSavingsInterestPayment, RzwSavingsPlan)
- RzwSavingsEnums.cs oluştur
- Trade entity'yi güncelle (TradeType enum + RzwSavingsAccountId)
- AppDbContext'i güncelle
- Migration oluştur ve uygula

**Faz 1 Başarı Kriterleri**:
- [x] Migration başarıyla çalışıyor ✅ TAMAMLANDI (2024-12-19)
- [x] Yeni tablolar oluşturuluyor ✅ TAMAMLANDI (2024-12-19)
- [x] LOCKED_BALANCE kolonu ekleniyor ✅ TAMAMLANDI (2024-12-19)
- [x] Build hataları yok ✅ TAMAMLANDI (2024-12-19)
- [x] Mevcut veriler korunuyor ✅ TAMAMLANDI (2024-12-19)

**🎉 FAZ 1 TAMAMLANDI** - Database & Models (2024-12-19)
- ✅ Wallet entity'sine LOCKED_BALANCE kolonu eklendi
- ✅ RZW Savings entity'leri oluşturuldu (RzwSavingsAccount, RzwSavingsInterestPayment, RzwSavingsPlan)
- ✅ RzwSavingsEnums.cs oluşturuldu (faiz oranları veritabanından alınacak şekilde düzenlendi)
- ✅ Trade entity'ye TradeType enum değerleri ve RzwSavingsAccountId eklendi
- ✅ AppDbContext güncellendi
- ✅ Migration oluşturuldu ve uygulandı (AddRzwSavingsSupport)
- ✅ Build başarılı, diagnostics temiz

### Adım 4: Test ve Commit
Faz 1 tamamlandıktan sonra:
```bash
# Build kontrolü
dotnet build

# Migration kontrolü
dotnet ef database update

# Test
dotnet test (eğer varsa)
```

### Adım 5: Sonraki Fazlara Geçiş
Faz 1 başarıyla tamamlandıktan sonra sırasıyla:
- ✅ Faz 1: Database & Models (TAMAMLANDI - 2024-12-19)
- ⏳ Faz 2: Balance Management (SONRAKİ)
- ⏸️ Faz 3: Savings System Core (BEKLEMEDE)
- ⏸️ Faz 4: Background Services (BEKLEMEDE)
- ⏸️ Faz 5: User Interface (BEKLEMEDE)
- ⏸️ Faz 6: Admin Panel & Testing (BEKLEMEDE)

## ⚠️ Kritik Kurallar

### Mevcut Sistemi Koruma
- **BALANCE kolonu değişmeyecek** (Available Balance olarak kalacak)
- **Mevcut WalletService metotları çalışmaya devam edecek**
- **Backward compatibility sağlanacak**
- **Mevcut Trade kayıtları etkilenmeyecek**

### Naming Conventions
- **Available Balance**: BALANCE kolonu
- **Locked Balance**: LOCKED_BALANCE kolonu (yeni)
- **Total Balance**: Available + Locked (hesaplanmış)
- **Metot isimleri**: available/locked/total terimleri içerecek

### Dependencies
- **TokenPriceService**: RZW token ID için kullan (sabit değer kullanma)
- **TradeService**: Audit trail için her wallet değişikliğinde Trade kaydı oluştur
- **Transaction Safety**: Database transaction'ları kullan

### Faiz Hesaplama Kuralları
- **Bileşik Faiz**: Günlük kapitalizasyon
- **Günlük Oran**: Plan tipine göre yıllık oranın günlüğe bölümü
- **Erken Çekim**: Tutulma süresine uygun daha düşük periyotlu plan faizi
- **Uygun Plan Yoksa**: Hiç faiz ödenmez

## 📊 Beklenen Sonuçlar

### Faz 1 Sonunda
- Database schema güncellenmiş
- Yeni entity'ler hazır
- Migration'lar çalışır durumda
- Mevcut sistem etkilenmemiş

### Proje Sonunda
- Kullanıcılar RZW vadeli hesap açabilecek
- Otomatik faiz ödemeleri çalışacak
- Admin panel ile yönetim yapılabilecek
- Comprehensive test coverage sağlanmış
- Production-ready sistem

## 🔧 Geliştirme Ortamı

### Çalıştırma Komutu
```bash
dotnet watch --artifacts-path ../../../../artifacts_augmentcode run --launch-profile httpAugmentCode
```

### Test Komutu
```bash
dotnet test src_unittests/
```

### Migration Komutları
```bash
dotnet ef migrations add [MigrationName]
dotnet ef database update
```

## 📝 Raporlama

Her faz tamamlandıktan sonra:
1. **Başarı kriterlerini kontrol et**
2. **Test sonuçlarını raporla**
3. **Karşılaşılan sorunları belirt**
4. **Sonraki faz için hazırlık durumunu bildir**

## 🎯 Sonraki Adım

**✅ FAZ 1 TAMAMLANDI** - Database & Models başarıyla uygulandı (2024-12-19)

**ŞİMDİ BAŞLA**: `docs/rzw-saving-feature/prompts/PHASE_2_BALANCE_MANAGEMENT.prompt` dosyasını oku ve Faz 2'yi uygula.

Faz 2 tamamlandıktan sonra bana rapor ver ve Faz 3 için onay iste.

---

**Önemli**: Her faz bağımsız olarak tamamlanabilir ve test edilebilir. Bir fazda sorun yaşarsan o fazı tamamlamadan sonrakine geçme.
