using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("RZW_SAVINGS_INTEREST_PAYMENT")]
    public class RzwSavingsInterestPayment
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("RZW_SAVINGS_ACCOUNT_ID")]
        public int RzwSavingsAccountId { get; set; }

        [Required]
        [Column("RZW_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal RzwAmount { get; set; }

        [Required]
        [Column("PAYMENT_DATE", TypeName = "datetime")]
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;

        [Column("WALLET_TRANSACTION_ID")]
        public int? WalletTransactionId { get; set; }

        [Column("DESCRIPTION")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("DAILY_RATE", TypeName = "decimal(10,6)")]
        public decimal? DailyRate { get; set; }

        [Column("PRINCIPAL_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal? PrincipalAmount { get; set; }

        [Column("ACCUMULATED_INTEREST", TypeName = "decimal(20,8)")]
        public decimal? AccumulatedInterest { get; set; }

        // Navigation properties
        [ForeignKey("RzwSavingsAccountId")]
        public virtual RzwSavingsAccount RzwSavingsAccount { get; set; } = null!;

        [ForeignKey("WalletTransactionId")]
        public virtual Trade? WalletTransaction { get; set; }
    }
}
