using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("RZW_SAVINGS_PLAN")]
    public class RzwSavingsPlan
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("NAME")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Column("TERM_TYPE")]
        [StringLength(20)]
        public string TermType { get; set; } = string.Empty;

        [Required]
        [Column("TERM_DURATION")]
        public int TermDuration { get; set; }

        [Required]
        [Column("INTEREST_RATE", TypeName = "decimal(10,6)")]
        public decimal InterestRate { get; set; }

        [Required]
        [Column("MIN_RZW_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal MinRzwAmount { get; set; }

        [Column("MAX_RZW_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal? MaxRzwAmount { get; set; }

        [Required]
        [Column("IS_ACTIVE")]
        public bool IsActive { get; set; } = true;

        [Column("DISPLAY_ORDER")]
        public int DisplayOrder { get; set; } = 0;

        [Column("DESCRIPTION")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        // Navigation properties
        public virtual ICollection<RzwSavingsAccount> SavingsAccounts { get; set; } = new List<RzwSavingsAccount>();
    }
}
