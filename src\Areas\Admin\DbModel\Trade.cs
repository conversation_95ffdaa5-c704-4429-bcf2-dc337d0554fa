using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("TRADE")]
    public class Trade
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("TYPE")]
        public TradeType Type { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("COIN_ID")]
        public int CoinId { get; set; }

        [Required]
        [Column("COIN_RATE",TypeName = "decimal(20,8)")]
        public decimal CoinRate { get; set; }

        [Required]
        [Column("COIN_AMOUNT",TypeName = "decimal(20,8)")]
        public decimal CoinAmount { get; set; }

        [Required]
        [Column("TRY_AMOUNT",TypeName = "decimal(20,2)")]
        public decimal TryAmount { get; set; }

        [Required]
        [Column("PREVIOUS_COIN_BALANCE",TypeName = "decimal(20,8)")]
        public decimal PreviousCoinBalance { get; set; }

        [Required]
        [Column("NEW_COIN_BALANCE",TypeName = "decimal(20,8)")]
        public decimal NewCoinBalance { get; set; }

        [Required]
        [Column("PREVIOUS_BALANCE",TypeName = "decimal(20,2)")]
        public decimal PreviousBalance { get; set; }

        [Required]
        [Column("NEW_BALANCE",TypeName = "decimal(20,2)")]
        public decimal NewBalance { get; set; }

        [Required]
        [Column("PREVIOUS_WALLET_BALANCE",TypeName = "decimal(20,8)")]
        public decimal PreviousWalletBalance { get; set; }

        [Required]
        [Column("NEW_WALLET_BALANCE",TypeName = "decimal(20,8)")]
        public decimal NewWalletBalance { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        [Column("STATUS")]
        public bool IsActive { get; set; } = true;

        [Column("REFERRAL_REWARD_ID")]
        public int? ReferralRewardId { get; set; }

        [Column("RZW_SAVINGS_ACCOUNT_ID")]
        public int? RzwSavingsAccountId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CoinId")]
        public virtual Market Coin { get; set; } = null!;

        [ForeignKey("ReferralRewardId")]
        public virtual ReferralReward? ReferralReward { get; set; }

        [ForeignKey("RzwSavingsAccountId")]
        public virtual RzwSavingsAccount? RzwSavingsAccount { get; set; }
    }

    public enum TradeType
    {
        Buy,            // alis
        Sell,           // satis
        PackageBonus,   // paket satın alımında verilen bonus
        ReferralReward, // yönlendirme ödülü

        // RZW Savings types
        RzwSavingsDeposit,      // vadeli hesaba yatırma
        RzwSavingsWithdrawal,   // vadeli hesaptan çekme
        RzwSavingsInterest,     // faiz ödemesi
        RzwSavingsEarlyWithdrawal, // erken çekim
        RzwSavingsMaturity      // vade dolma
    }
}
