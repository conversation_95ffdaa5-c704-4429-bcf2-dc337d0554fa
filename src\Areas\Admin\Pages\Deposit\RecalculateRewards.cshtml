@page
@model RazeWinComTr.Areas.Admin.Pages.Deposit.RecalculateRewardsModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Recalculate Rewards"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Recalculate Rewards"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/Deposit">@L["Payments"]</a></li>
                    <li class="breadcrumb-item active">@L["Recalculate Rewards"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (Model.AlertMessage != null)
        {
            <script id="scriptMessage" type="text/javascript">
                window.onload = function () {
                    Swal.fire({
                        title: '@Html.Raw(Model.AlertMessage.Title)',
                        text: '@Html.Raw(Model.AlertMessage.Text)',
                        icon: '@Html.Raw(Model.AlertMessage.Icon)'
                    }).then((result) => {
                        var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                        if (result.isConfirmed && redirectUrl !== '') {
                            location.href = redirectUrl;
                        }
                    });
                    $('#scriptMessage').remove();
                }
            </script>
        }

        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">@L["Recalculate Rewards Parameters"]</h3>
            </div>
            <form method="post">
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> @L["Information"]</h5>
                        <p>@L["This tool will recalculate rewards for deposits in the specified date range. It will update the TL and RZW percentages and amounts based on the current package reward percentages."]</p>
                        <p>@L["This is useful when you have updated the TL and RZW percentages for packages and want to apply these changes to existing rewards."]</p>
                        <p><strong>@L["Warning:"]</strong> @L["This operation will modify existing rewards and add TL amounts to user balances. Make sure you understand the implications before proceeding."]</p>
                    </div>

                    <div class="form-group">
                        <label for="startDate">@L["Start Date"]</label>
                        <input type="date" id="startDate" name="StartDate" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-dd")" required />
                    </div>

                    <div class="form-group">
                        <label for="endDate">@L["End Date"]</label>
                        <input type="date" id="endDate" name="EndDate" class="form-control" value="@Model.EndDate.ToString("yyyy-MM-dd")" required />
                    </div>

                    @if (Model.IsPreview && Model.RecalculationResult != null)
                    {
                        <div class="alert alert-success">
                            <h5><i class="icon fas fa-check"></i> @L["Preview Results"]</h5>
                            <p>@L["Found {0} rewards to recalculate in the specified date range.", Model.RecalculationResult.RewardsCount]</p>
                            <p>@L["Total TL amount to be distributed: {0} TL", Model.RecalculationResult.TotalTlAmount.ToString("N2")]</p>
                            <p>@L["Total RZW amount to be distributed: {0} RZW", Model.RecalculationResult.TotalRzwAmount.ToString("N2")]</p>
                            <p>@L["Affected users: {0}", Model.RecalculationResult.AffectedUsersCount]</p>
                        </div>
                    }
                    else if (Model.IsProcessed && Model.RecalculationResult != null)
                    {
                        <div class="alert alert-success">
                            <h5><i class="icon fas fa-check"></i> @L["Recalculation Completed"]</h5>
                            <p>@L["Successfully recalculated {0} rewards.", Model.RecalculationResult.RewardsCount]</p>
                            <p>@L["Total TL amount distributed: {0} TL", Model.RecalculationResult.TotalTlAmount.ToString("N2")]</p>
                            <p>@L["Affected users: {0}", Model.RecalculationResult.AffectedUsersCount]</p>
                        </div>
                    }
                </div>
                <div class="card-footer">
                    <button type="submit" name="action" value="preview" class="btn btn-info">
                        <i class="fas fa-search mr-1"></i> @L["Preview"]
                    </button>
                    <button type="submit" name="action" value="process" class="btn btn-primary" @(Model.IsPreview ? "" : "disabled")>
                        <i class="fas fa-cogs mr-1"></i> @L["Process Recalculation"]
                    </button>
                    <a href="/Admin/Deposit" class="btn btn-default">
                        <i class="fas fa-arrow-left mr-1"></i> @L["Back to Payments"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>
