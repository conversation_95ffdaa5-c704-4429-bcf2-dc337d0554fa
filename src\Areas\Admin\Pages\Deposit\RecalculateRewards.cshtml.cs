using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit
{
    public class RecalculateRewardsModel : PageModel
    {
        private readonly ReferralRewardService _referralRewardService;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public RecalculateRewardsModel(
            ReferralRewardService referralRewardService,
            IStringLocalizer<SharedResource> localizer)
        {
            _referralRewardService = referralRewardService;
            _localizer = localizer;
        }

        [BindProperty]
        public DateTime StartDate { get; set; } = DateTime.UtcNow.AddMonths(-1);

        [BindProperty]
        public DateTime EndDate { get; set; } = DateTime.UtcNow;

        public SweetAlert2Message? AlertMessage { get; set; }

        public bool IsPreview { get; set; }
        public bool IsProcessed { get; set; }

        public RecalculationResult? RecalculationResult { get; set; }

        public void OnGet()
        {
            // Default view
        }

        public async Task<IActionResult> OnPostAsync(string action)
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                if (action == "preview")
                {
                    // Preview mode - calculate but don't apply changes
                    var rewardsCount = await _referralRewardService.CountRewardsInDateRangeAsync(StartDate, EndDate);
                    var totalTlAmount = await _referralRewardService.CalculateTotalTlAmountForDateRangeAsync(StartDate, EndDate);
                    var totalRzwAmount = await _referralRewardService.CalculateTotalRzwAmountForDateRangeAsync(StartDate, EndDate);
                    var affectedUsersCount = await _referralRewardService.CountAffectedUsersInDateRangeAsync(StartDate, EndDate);

                    RecalculationResult = new RecalculationResult
                    {
                        RewardsCount = rewardsCount,
                        TotalTlAmount = totalTlAmount,
                        TotalRzwAmount = totalRzwAmount,
                        AffectedUsersCount = affectedUsersCount
                    };

                    IsPreview = true;
                }
                else if (action == "process")
                {
                    // Process mode - apply changes
                    var recalculatedCount = await _referralRewardService.RecalculateRewardsAsync(StartDate, EndDate);
                    var totalTlAmount = await _referralRewardService.CalculateTotalTlAmountForDateRangeAsync(StartDate, EndDate);
                    var totalRzwAmount = await _referralRewardService.CalculateTotalRzwAmountForDateRangeAsync(StartDate, EndDate);
                    var affectedUsersCount = await _referralRewardService.CountAffectedUsersInDateRangeAsync(StartDate, EndDate);

                    RecalculationResult = new RecalculationResult
                    {
                        RewardsCount = recalculatedCount,
                        TotalTlAmount = totalTlAmount,
                        TotalRzwAmount = totalRzwAmount,
                        AffectedUsersCount = affectedUsersCount
                    };

                    IsProcessed = true;
                    AlertMessage = new SweetAlert2Message
                    {
                        Title = _localizer["Success"],
                        Text = _localizer["Successfully recalculated {0} rewards.", recalculatedCount],
                        Icon = "success"
                    };
                }

                return Page();
            }
            catch (Exception ex)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = _localizer["An error occurred: {0}", ex.Message],
                    Icon = "error"
                };
                return Page();
            }
        }
    }

    public class RecalculationResult
    {
        public int RewardsCount { get; set; }
        public decimal TotalTlAmount { get; set; }
        public decimal TotalRzwAmount { get; set; }
        public int AffectedUsersCount { get; set; }
    }
}
