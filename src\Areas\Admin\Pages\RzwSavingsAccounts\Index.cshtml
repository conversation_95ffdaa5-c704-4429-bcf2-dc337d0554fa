@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Savings Accounts"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Savings Accounts"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item active">@L["Savings Accounts"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Filters -->
        <div class="card collapsed-card">
            <div class="card-header">
                <h3 class="card-title">@L["Filters"]</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <form method="get">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="SearchEmail">@L["User Email"]</label>
                                <input asp-for="SearchEmail" class="form-control" placeholder="@L["Enter email"]" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="Status">@L["Status"]</label>
                                <select asp-for="Status" class="form-control">
                                    <option value="">@L["All"]</option>
                                    <option value="Active">@L["Active"]</option>
                                    <option value="Matured">@L["Matured"]</option>
                                    <option value="Withdrawn">@L["Withdrawn"]</option>
                                    <option value="Cancelled">@L["Cancelled"]</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="StartDate">@L["Start Date"]</label>
                                <input asp-for="StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="EndDate">@L["End Date"]</label>
                                <input asp-for="EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">@L["Filter"]</button>
                                    <a href="/Admin/RzwSavingsAccounts" class="btn btn-secondary">@L["Clear"]</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Accounts Table -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Total"]: @Model.TotalCount @L["accounts"]</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                        <tr>
                            <th>@L["ID"]</th>
                            <th>@L["User"]</th>
                            <th>@L["Plan"]</th>
                            <th>@L["Amount"]</th>
                            <th>@L["Interest Rate"]</th>
                            <th>@L["Start Date"]</th>
                            <th>@L["Maturity Date"]</th>
                            <th>@L["Status"]</th>
                            <th>@L["Progress"]</th>
                            <th>@L["Earned"]</th>
                            <th style="width: 120px">@L["Actions"]</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach (var account in Model.Accounts)
                        {
                            <tr>
                                <td>@account.Id</td>
                                <td>@account.UserEmail</td>
                                <td>@account.PlanName</td>
                                <td>@account.RzwAmount.ToString("N8")</td>
                                <td>@account.InterestRateDisplayText</td>
                                <td>@account.StartDate.ToString("yyyy-MM-dd")</td>
                                <td>@account.MaturityDate.ToString("yyyy-MM-dd")</td>
                                <td>
                                    <span class="badge @account.StatusBadgeClass">
                                        @account.StatusDisplayText
                                    </span>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: @account.ProgressPercentage%"
                                             aria-valuenow="@account.ProgressPercentage" 
                                             aria-valuemin="0" aria-valuemax="100">
                                            @account.ProgressPercentage%
                                        </div>
                                    </div>
                                </td>
                                <td>@account.TotalEarnedRzw.ToString("N8")</td>
                                <td>
                                    <a href="/Admin/RzwSavingsAccounts/Details?id=@account.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if (account.IsActive)
                                    {
                                        <a href="/Admin/RzwSavingsAccounts/Edit?id=@account.Id" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    }
                                </td>
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            @if (Model.HasPreviousPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?pageNumber=@(Model.PageNumber - 1)&searchEmail=@Model.SearchEmail&status=@Model.Status&startDate=@Model.StartDate&endDate=@Model.EndDate">@L["Previous"]</a>
                                </li>
                            }
                            
                            @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                            {
                                <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                    <a class="page-link" href="?pageNumber=@i&searchEmail=@Model.SearchEmail&status=@Model.Status&startDate=@Model.StartDate&endDate=@Model.EndDate">@i</a>
                                </li>
                            }
                            
                            @if (Model.HasNextPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?pageNumber=@(Model.PageNumber + 1)&searchEmail=@Model.SearchEmail&status=@Model.Status&startDate=@Model.StartDate&endDate=@Model.EndDate">@L["Next"]</a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</section>
