using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts;

public class IndexModel : PageModel
{
    private readonly RzwSavingsService _savingsService;

    public IndexModel(RzwSavingsService savingsService)
    {
        _savingsService = savingsService;
    }

    public List<RzwSavingsAccountViewModel> Accounts { get; set; } = new();

    [BindProperty(SupportsGet = true)]
    public string? SearchEmail { get; set; }

    [BindProperty(SupportsGet = true)]
    public string? Status { get; set; }

    [BindProperty(SupportsGet = true)]
    public int? PlanId { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? StartDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? EndDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public int PageNumber { get; set; } = 1;

    [BindProperty(SupportsGet = true)]
    public int PageSize { get; set; } = 50;

    public int TotalCount { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;

    public async Task OnGetAsync()
    {
        var result = await _savingsService.GetAccountsForAdminAsync(
            searchEmail: SearchEmail,
            status: Status,
            planId: PlanId,
            startDate: StartDate,
            endDate: EndDate,
            pageNumber: PageNumber,
            pageSize: PageSize);

        Accounts = result.Accounts;
        TotalCount = result.TotalCount;
    }
}
