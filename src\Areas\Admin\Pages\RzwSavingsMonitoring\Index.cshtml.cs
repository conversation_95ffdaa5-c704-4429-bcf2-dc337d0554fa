using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsMonitoring;

public class IndexModel : PageModel
{
    private readonly RzwSavingsMonitoringService _monitoringService;

    public IndexModel(RzwSavingsMonitoringService monitoringService)
    {
        _monitoringService = monitoringService;
    }

    public SystemHealthInfo SystemHealth { get; set; } = new();
    public PerformanceMetrics Performance { get; set; } = new();
    public List<PlanPerformanceInfo> PlanPerformance { get; set; } = new();

    public async Task OnGetAsync()
    {
        SystemHealth = await _monitoringService.GetSystemHealthAsync();
        Performance = await _monitoringService.GetPerformanceMetricsAsync();
        PlanPerformance = await _monitoringService.GetPlanPerformanceAsync();
    }
}
