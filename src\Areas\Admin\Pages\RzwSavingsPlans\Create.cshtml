@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.CreateModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Create Plan"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Create Plan"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/RzwSavingsPlans">@L["Savings Plans"]</a></li>
                    <li class="breadcrumb-item active">@L["Create Plan"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Plan Details"]</h3>
            </div>
            <form method="post">
                <div class="card-body">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Plan.Name" class="control-label">@L["Name"]</label>
                                <input asp-for="Plan.Name" class="form-control" />
                                <span asp-validation-for="Plan.Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Plan.DisplayOrder" class="control-label">@L["Display Order"]</label>
                                <input asp-for="Plan.DisplayOrder" class="form-control" type="number" />
                                <span asp-validation-for="Plan.DisplayOrder" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Plan.TermType" class="control-label">@L["Term Type"]</label>
                                <select asp-for="Plan.TermType" class="form-control">
                                    <option value="Daily">@L["Daily"]</option>
                                    <option value="Monthly">@L["Monthly"]</option>
                                    <option value="Yearly">@L["Yearly"]</option>
                                </select>
                                <span asp-validation-for="Plan.TermType" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Plan.TermDuration" class="control-label">@L["Term Duration"]</label>
                                <input asp-for="Plan.TermDuration" class="form-control" type="number" min="1" />
                                <span asp-validation-for="Plan.TermDuration" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Plan.InterestRate" class="control-label">@L["Interest Rate"] (%)</label>
                                <input asp-for="Plan.InterestRate" class="form-control" type="number" max="100" step="any" />
                                <span asp-validation-for="Plan.InterestRate" class="text-danger"></span>
                                <small class="form-text text-muted">@L["Enter as percentage (e.g., 0.03 for 0.03%)"]</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Plan.MinRzwAmount" class="control-label">@L["Min Amount"] (RZW)</label>
                                <input asp-for="Plan.MinRzwAmount" class="form-control" type="number" />
                                <span asp-validation-for="Plan.MinRzwAmount" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Plan.MaxRzwAmount" class="control-label">@L["Max Amount"] (RZW)</label>
                                <input asp-for="Plan.MaxRzwAmount" class="form-control" type="number" />
                                <span asp-validation-for="Plan.MaxRzwAmount" class="text-danger"></span>
                                <small class="form-text text-muted">@L["Leave empty for unlimited"]</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="Plan.Description" class="control-label">@L["Description"]</label>
                        <textarea asp-for="Plan.Description" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Plan.Description" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="Plan.IsActive" class="custom-control-input" />
                            <label asp-for="Plan.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-success">@L["Create Plan"]</button>
                    <a asp-page="./Index" class="btn btn-secondary">@L["Cancel"]</a>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
