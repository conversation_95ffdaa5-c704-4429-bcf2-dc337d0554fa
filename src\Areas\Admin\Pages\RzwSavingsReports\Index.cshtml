@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsReports.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Reports"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["RZW Savings Reports"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item active">@L["Reports"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Date Range Filter -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Report Filters"]</h3>
            </div>
            <div class="card-body">
                <form method="get">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="StartDate">@L["Start Date"]</label>
                                <input asp-for="StartDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="EndDate">@L["End Date"]</label>
                                <input asp-for="EndDate" type="date" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">@L["Generate Report"]</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>@Model.SystemHealth.TotalActiveAccounts</h3>
                        <p>@L["Total Active Accounts"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>@Model.SystemHealth.TotalLockedRzw.ToString("N0")</h3>
                        <p>@L["Total Locked RZW"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-lock"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>@Model.Performance.NewAccountsLast30Days</h3>
                        <p>@L["New Accounts"] (30d)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>@Model.Performance.TotalInterestPaidLast30Days.ToString("N0")</h3>
                        <p>@L["Interest Paid"] (30d)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Performance Report -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Plan Performance Report"]</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" onclick="window.print()">
                                <i class="fas fa-print"></i> @L["Print"]
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>@L["Plan Name"]</th>
                                    <th>@L["Term"]</th>
                                    <th>@L["Interest Rate"]</th>
                                    <th>@L["Total Accounts"]</th>
                                    <th>@L["Active Accounts"]</th>
                                    <th>@L["Locked RZW"]</th>
                                    <th>@L["Interest Paid"]</th>
                                    <th>@L["Avg Account Size"]</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach (var plan in Model.PlanPerformance)
                                {
                                    <tr>
                                        <td>@plan.PlanName</td>
                                        <td>@plan.TermDuration @plan.TermType</td>
                                        <td>@((plan.InterestRate * 100).ToString("N4"))%</td>
                                        <td>@plan.TotalAccounts</td>
                                        <td>@plan.ActiveAccounts</td>
                                        <td>@plan.TotalLockedRzw.ToString("N8")</td>
                                        <td>@plan.TotalInterestPaid.ToString("N8")</td>
                                        <td>@((plan.ActiveAccounts > 0 ? plan.TotalLockedRzw / plan.ActiveAccounts : 0).ToString("N8"))</td>
                                    </tr>
                                }
                                </tbody>
                                <tfoot>
                                <tr class="font-weight-bold">
                                    <td colspan="3">@L["Total"]</td>
                                    <td>@Model.PlanPerformance.Sum(p => p.TotalAccounts)</td>
                                    <td>@Model.PlanPerformance.Sum(p => p.ActiveAccounts)</td>
                                    <td>@Model.PlanPerformance.Sum(p => p.TotalLockedRzw).ToString("N8")</td>
                                    <td>@Model.PlanPerformance.Sum(p => p.TotalInterestPaid).ToString("N8")</td>
                                    <td>-</td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Account Activity"] (30 @L["Days"])</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-6">@L["New Accounts"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.NewAccountsLast30Days</dd>

                            <dt class="col-sm-6">@L["Early Withdrawals"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.EarlyWithdrawalsLast30Days</dd>

                            <dt class="col-sm-6">@L["Matured Accounts"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.MaturedAccountsLast30Days</dd>

                            <dt class="col-sm-6">@L["Total Interest Paid"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.TotalInterestPaidLast30Days.ToString("N8") RZW</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["System Status"]</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-6">@L["Pending Interest Payments"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.PendingInterestPayments</dd>

                            <dt class="col-sm-6">@L["Matured Accounts"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.MaturedAccounts</dd>

                            <dt class="col-sm-6">@L["Today's Interest Payments"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.TodayInterestCount</dd>

                            <dt class="col-sm-6">@L["Today's Interest Amount"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.TodayInterestPayments.ToString("N8") RZW</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
