using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsReports;

public class IndexModel : PageModel
{
    private readonly RzwSavingsMonitoringService _monitoringService;

    public IndexModel(RzwSavingsMonitoringService monitoringService)
    {
        _monitoringService = monitoringService;
    }

    public SystemHealthInfo SystemHealth { get; set; } = new();
    public PerformanceMetrics Performance { get; set; } = new();
    public List<PlanPerformanceInfo> PlanPerformance { get; set; } = new();

    [BindProperty(SupportsGet = true)]
    public DateTime? StartDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? EndDate { get; set; }

    public async Task OnGetAsync()
    {
        // Set default date range if not provided
        if (!StartDate.HasValue)
            StartDate = DateTime.UtcNow.AddDays(-30);
        
        if (!EndDate.HasValue)
            EndDate = DateTime.UtcNow;

        SystemHealth = await _monitoringService.GetSystemHealthAsync();
        Performance = await _monitoringService.GetPerformanceMetricsAsync();
        PlanPerformance = await _monitoringService.GetPlanPerformanceAsync();
    }
}
