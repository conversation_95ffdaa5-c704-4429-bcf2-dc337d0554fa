namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

/// <summary>
/// Contains RZW token information including ID and price
/// </summary>
public class RzwTokenInfo
{
    /// <summary>
    /// The RZW token ID in the Markets table
    /// </summary>
    public int TokenId { get; set; }

    /// <summary>
    /// The current RZW token buy price in TRY
    /// </summary>
    public decimal BuyPrice { get; set; }

    /// <summary>
    /// The current RZW token sell price in TRY
    /// </summary>
    public decimal SellPrice { get; set; }
}

public interface ITokenPriceService
{
    /// <summary>
    /// Gets the current RZW token buy price in TRY
    /// </summary>
    /// <returns>The current RZW token buy price</returns>
    /// <exception cref="InvalidOperationException">Thrown when the price is not available or invalid</exception>
    Task<decimal> GetCurrentRzwBuyPriceAsync();

    /// <summary>
    /// Gets the RZW token ID from the Markets table
    /// </summary>
    /// <returns>The RZW token ID</returns>
    /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found in the system</exception>
    Task<int> GetRzwTokenIdAsync();

    [Obsolete]
    /// <summary>
    /// Gets complete RZW token information including ID and prices in a single database query
    /// </summary>
    /// <returns>RZW token information</returns>
    /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found or prices are invalid</exception>
    Task<RzwTokenInfo> GetRzwTokenInfoAsync();

    /// <summary>
    /// Gets complete RZW token information including ID and prices in a single database query
    /// </summary>
    /// <returns>RZW token information</returns>
    /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found or prices are invalid</exception>
    Task<RzwTokenInfo> GetCoinInfoAsync(int coinId);
}
