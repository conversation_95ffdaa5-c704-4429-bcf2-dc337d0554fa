using Microsoft.EntityFrameworkCore.Storage;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

public interface IWalletService
{
    // Existing methods - UNCHANGED
    Task<Wallet?> GetByIdAsync(int id);
    Task<List<Wallet>> GetByUserIdAsync(int userId);
    Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN);
    Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId);
    Task<List<WalletViewModel>> GetListAsync();
    Task<Wallet> CreateAsync(Wallet wallet, IDbContextTransaction? existingTransaction = null);
    Task UpdateAsync(Wallet wallet);
    Task DeleteAsync(int id);

    // BACKWARD COMPATIBILITY METHODS - Obsolete
    [Obsolete("Use GetUserAvailableBalanceAsync instead")]
    Task<decimal> GetUserBalanceAsync(int userId, int coinId);

    [Obsolete("Use AddAvailableBalanceAsync instead")]
    Task<Wallet> AddBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);

    [Obsolete("Use DeductAvailableBalanceAsync instead")]
    Task<bool> DeductBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);

    [Obsolete("Use AddToAvailableBalanceAsync instead")]
    Task<Wallet> AddToWalletAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);

    // NEW METHODS - Available Balance
    Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId);
    Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType);
    Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, IDbContextTransaction transaction);
    Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);
    Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction transaction);
    Task<Wallet> AddToAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);

    // NEW METHODS - Locked Balance
    Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId);
    Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);
    Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction? transaction);
    Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);
    Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction transaction);
    //Task<Wallet> AddLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);
    //Task<bool> DeductLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount);

    // NEW METHODS - Total Balance
    Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId);

    // NEW METHODS - Balance Info
    Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId);
    Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId);
}
