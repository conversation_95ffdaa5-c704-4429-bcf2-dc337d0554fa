using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for managing referral rewards
/// </summary>
public class ReferralRewardService
{
    private readonly AppDbContext _context;
    private readonly IWalletService _walletService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly ILogger<ReferralRewardService> _logger;
    private readonly TradeService _tradeService;

    public ReferralRewardService(
        AppDbContext context,
        IWalletService walletService,
        ITokenPriceService tokenPriceService,
        ILogger<ReferralRewardService> logger,
        TradeService tradeService)
    {
        _context = context;
        _walletService = walletService;
        _tokenPriceService = tokenPriceService;
        _logger = logger;
        _tradeService = tradeService;
    }

    /// <summary>
    /// Gets all rewards for a specific user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>List of referral rewards</returns>
    public async Task<List<ReferralReward>> GetUserRewardsAsync(int userId)
    {
        return await _context.ReferralRewards
            .Include(r => r.ReferredUser)
            .Include(r => r.Package)
            .Where(r => r.UserId == userId)
            .OrderByDescending(r => r.CreatedDate)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a summary of referral rewards for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>Summary of referral rewards</returns>
    public async Task<ReferralRewardSummary> GetUserRewardSummaryAsync(int userId)
    {
        // Check if the user exists
        var userExists = await _context.Users.AnyAsync(u => u.UserId == userId);
        if (!userExists)
        {
            throw new ArgumentException($"User with ID {userId} does not exist", nameof(userId));
        }

        var rewards = await _context.ReferralRewards
            .Where(r => r.UserId == userId)
            .ToListAsync();

        var summary = new ReferralRewardSummary
        {
            TotalRzwRewards = rewards.Sum(r => r.RzwAmount),
            TotalTlRewards = rewards.Sum(r => r.TlAmount),
            TotalReferrals = rewards.Select(r => r.ReferredUserId).Distinct().Count()
        };

        // Group RZW rewards by level
        var rzwRewardsByLevel = rewards
            .GroupBy(r => r.Level)
            .ToDictionary(
                g => g.Key,
                g => g.Sum(r => r.RzwAmount)
            );

        // Group TL rewards by level
        var tlRewardsByLevel = rewards
            .GroupBy(r => r.Level)
            .ToDictionary(
                g => g.Key,
                g => g.Sum(r => r.TlAmount)
            );

        // Group referrals by level
        var referralsByLevel = rewards
            .GroupBy(r => r.Level)
            .ToDictionary(
                g => g.Key,
                g => g.Select(r => r.ReferredUserId).Distinct().Count()
            );

        summary.RzwRewardsByLevel = rzwRewardsByLevel;
        summary.TlRewardsByLevel = tlRewardsByLevel;
        summary.ReferralsByLevel = referralsByLevel;

        return summary;
    }

    /// <summary>
    /// Gets the current referral reward percentages for a specific package
    /// </summary>
    /// <param name="packageId">The package ID</param>
    /// <returns>Dictionary of level to percentage mappings</returns>
    public async Task<Dictionary<int, decimal>> GetReferralPercentagesAsync(int packageId)
    {
        var percentages = new Dictionary<int, decimal>();

        // Get all percentages for this package from the database
        var packagePercentages = await _context.PackageRewardPercentages
            .Where(p => p.PackageId == packageId)
            .ToListAsync();

        foreach (var percentage in packagePercentages)
        {
            percentages[percentage.Level] = percentage.RzwPercentage;
        }

        return percentages;
    }

    /// <summary>
    /// Processes referral rewards for a deposit
    /// </summary>
    /// <param name="depositId">The payment ID</param>
    /// <returns>Summary of the reward distribution</returns>
    public async Task<DepositRewardSummary> ProcessDepositRewardsAsync(int depositId)
    {
        var deposit = await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == depositId);

        if (deposit is null)
            throw new ArgumentException("Deposit not found", nameof(depositId));

        // If payment is not approved, don't process rewards
        if (deposit.Status != DepositStatus.Approved)
            throw new InvalidOperationException("Cannot distribute rewards for non-approved deposits");

        // If rewards have already been distributed, don't process again
        if (deposit.RewardStatus == DepositRewardStatus.Distributed ||
            deposit.RewardStatus == DepositRewardStatus.NoRewards)
        {
            // Just return the summary of already distributed rewards
            return await GetDepositRewardSummaryAsync(depositId);
        }

        try
        {
            // Mark as being processed
            deposit.RewardStatus = DepositRewardStatus.Processed;
            await _context.SaveChangesAsync();

            // Use CalculatePotentialRewardsAsync to calculate potential rewards
            // This eliminates duplication in finding referrers and calculating reward amounts
            var potentialRewards = await CalculatePotentialRewardsAsync(depositId);

            // If no rewards to distribute, update status and return
            if (potentialRewards.RewardedUsersCount == 0)
            {
                _logger.LogInformation("No referrers with active packages found for user {UserId}", deposit.User.UserId);
                deposit.RewardStatus = DepositRewardStatus.NoRewards;
                await _context.SaveChangesAsync();
                return await GetDepositRewardSummaryAsync(depositId);
            }

            // Get RZW token information for wallet operations
            var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
            int rzwTokenId = rzwTokenInfo.TokenId;

            // Distribute rewards
            var rewardsDistributed = new List<ReferralReward>();
            var nowUtc = DateTime.UtcNow;
            // Process each potential reward
            foreach (var rewardDetail in potentialRewards.Rewards)
            {
                // Create reward record
                var reward = new ReferralReward
                {
                    UserId = rewardDetail.UserId,
                    ReferredUserId = rewardDetail.ReferredUserId,
                    PackageId = rewardDetail.PackageId ?? 0,
                    DepositId = depositId,
                    Level = rewardDetail.Level,
                    RzwAmount = rewardDetail.RzwAmount,
                    TlAmount = rewardDetail.TlAmount,
                    RzwPercentage = rewardDetail.RzwPercentage,
                    TlPercentage = rewardDetail.TlPercentage,
                    DepositAmount = rewardDetail.DepositAmount,
                    Status = ReferralRewardStatus.Paid,
                    DepositDate = nowUtc,
                    RzwPrice = rewardDetail.RzwBuyPrice,
                    RewardType = rewardDetail.RewardType
                };

                _context.ReferralRewards.Add(reward);
                rewardsDistributed.Add(reward);

                // Get the user's current RZW wallet balance before adding tokens
                var currentWalletBalance = await _walletService.GetUserAvailableBalanceAsync(rewardDetail.UserId, rzwTokenId);

                // Add the RZW reward to the referrer's RZW wallet
                var updatedWallet = await _walletService.AddToAvailableBalanceAsync(rewardDetail.UserId, rzwTokenInfo, rewardDetail.RzwAmount);

                _logger.LogInformation("Added {Amount} RZW to wallet of user {UserId}", rewardDetail.RzwAmount, rewardDetail.UserId);

                // Get the user's TRY balance before adding TL reward
                var user = await _context.Users.FindAsync(rewardDetail.UserId);
                if (user != null)
                {
                    var userTryBalance = user.Balance;

                    // Add the TL reward to the referrer's TL balance
                    user.Balance += rewardDetail.TlAmount;
                    user.ModDate = DateTime.UtcNow;
                    _context.Users.Update(user);

                    // Create a balance transaction record for the TL reward
                    var balanceTransaction = new BalanceTransaction
                    {
                        IsActive = true,
                        UserId = rewardDetail.UserId,
                        Amount = rewardDetail.TlAmount,
                        PreviousBalance = userTryBalance,
                        NewBalance = user.Balance,
                        TransactionType = TransactionType.ReferralReward,
                        ReferenceId = depositId,
                        ReferenceType = BalanceTransactionReferenceTypes.Deposit,
                        ReferralRewardId = reward.Id,
                        Description = $"Referral reward for deposit #{depositId} (Level {rewardDetail.Level})",
                        CreatedDate = DateTime.UtcNow
                    };

                    _context.BalanceTransactions.Add(balanceTransaction);

                    _logger.LogInformation("Added {Amount} TL to balance of user {UserId}", rewardDetail.TlAmount, rewardDetail.UserId);

                    // Create a trade record for the RZW tokens added as referral reward
                    var trade = new Trade
                    {
                        UserId = rewardDetail.UserId,
                        CoinRate = rewardDetail.RzwBuyPrice,
                        CoinAmount = rewardDetail.RzwAmount,
                        TryAmount = rewardDetail.RzwAmount * rewardDetail.RzwBuyPrice, // TL equivalent of RZW reward
                        Type = TradeType.ReferralReward, // Using ReferralReward type for referral rewards
                        CoinId = rzwTokenId,
                        PreviousCoinBalance = currentWalletBalance,
                        NewCoinBalance = updatedWallet.Balance,
                        PreviousBalance = userTryBalance, // Original TRY balance
                        NewBalance = userTryBalance, // Updated TRY balance after TL reward
                        PreviousWalletBalance = currentWalletBalance,
                        NewWalletBalance = updatedWallet.Balance,
                        ReferralRewardId = reward.Id,
                        CreatedDate = nowUtc,
                        IsActive = true
                    };

                    await _tradeService.CreateAsync(trade);

                    _logger.LogInformation("Created trade record for referral reward of {Amount} RZW to user {UserId}", rewardDetail.RzwAmount, rewardDetail.UserId);
                }
            }

            // Update payment status
            if (rewardsDistributed.Count > 0)
            {
                deposit.RewardStatus = DepositRewardStatus.Distributed;
            }
            else
            {
                deposit.RewardStatus = DepositRewardStatus.NoRewards;
            }

            // Save changes
            await _context.SaveChangesAsync();

            // Return summary using GetDepositRewardSummaryAsync to avoid duplication
            // This ensures we get a consistent summary format
            return await GetDepositRewardSummaryAsync(depositId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing referral rewards for payment {DepositId}", depositId);

            // Update payment status on error
            deposit.RewardStatus = DepositRewardStatus.Failed;
            await _context.SaveChangesAsync();

            throw;
        }
    }

    /// <summary>
    /// Gets the reward summary for a payment
    /// </summary>
    /// <param name="depositId">The payment ID</param>
    /// <returns>Summary of the reward distribution</returns>
    public async Task<DepositRewardSummary> GetDepositRewardSummaryAsync(int depositId)
    {
        // Ödeme bilgisini al
        var deposit = await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == depositId);

        if (deposit is null)
            throw new ArgumentException("Deposit not found", nameof(depositId));

        // Bu ödemeye ait tüm ödülleri al
        var rewards = await _context.ReferralRewards
            .Include(r => r.User)
            .Include(r => r.ReferredUser)
            .Include(r => r.Package) // Include the Package entity to get the name
            .Where(r => r.DepositId == depositId && r.Status == ReferralRewardStatus.Paid)
            .ToListAsync();

        // Özet bilgileri hesapla
        var summary = new DepositRewardSummary
        {
            DepositId = depositId,
            DepositAmount = deposit.Amount,
            DepositDate = deposit.CreatedDate,
            UserEmail = deposit.User.Email,
            UserFullName = $"{deposit.User.Name} {deposit.User.Surname}",
            RewardStatus = deposit.RewardStatus,
            ProcessDate = rewards.Count > 0 ? rewards.Max(r => r.DepositDate) : null,
            RewardedUsersCount = rewards.Count,
            TotalRzwDistributed = rewards.Sum(r => r.RzwAmount),
            TotalTlDistributed = rewards.Sum(r => r.TlAmount),
            RzwBuyPrice = rewards.Count > 0 ? rewards.First().RzwPrice : 0,
            Rewards = rewards.Select(r => new RewardDetailViewModel
            {
                Id = r.Id,
                UserId = r.UserId,
                UserEmail = r.User.Email,
                UserFullName = $"{r.User.Name} {r.User.Surname}",
                ReferredUserId = r.ReferredUserId,
                ReferredUserEmail = r.ReferredUser.Email,
                ReferredUserFullName = $"{r.ReferredUser.Name} {r.ReferredUser.Surname}",
                Level = r.Level,
                RzwAmount = r.RzwAmount,
                TlAmount = r.TlAmount,
                RzwPercentage = r.RzwPercentage,
                TlPercentage = r.TlPercentage,
                DepositAmount = r.DepositAmount,
                RzwBuyPrice = r.RzwPrice,
                RewardType = r.RewardType,
                DepositDate = r.DepositDate ?? DateTime.UtcNow,
                PackageId = r.PackageId,
                PackageName = r.Package?.Name ?? string.Empty
            }).ToList()
        };

        return summary;
    }

    /// <summary>
    /// Calculates potential rewards for a deposit without actually distributing them
    /// </summary>
    /// <param name="depositId">The deposit ID</param>
    /// <returns>Preview of the potential reward distribution</returns>
    public async Task<DepositRewardSummary> CalculatePotentialRewardsAsync(int depositId)
    {
        var deposit = await _context.Deposits
            .Include(p => p.User)
            .FirstOrDefaultAsync(p => p.Id == depositId);

        if (deposit is null)
            throw new ArgumentException("Deposit not found", nameof(depositId));

        // If payment is not approved, don't process rewards
        if (deposit.Status != DepositStatus.Approved)
            throw new InvalidOperationException("Cannot calculate rewards for non-approved deposits");

        // Get the user who made the deposit
        var depositUser = deposit.User;
        var depositUserId = depositUser.UserId;
        var depositAmount = deposit.Amount;

        // Get RZW token information (ID and price) in a single query
        var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
        int rzwTokenId = rzwTokenInfo.TokenId;
        decimal rzwBuyPrice = rzwTokenInfo.BuyPrice;

        // Find the referral chain (up to 10 levels for safety)
        var referrers = new List<(User User, int Level, int? PackageId)>();
        var currentUser = depositUser;
        int level = 0;
        var nowUtc = DateTime.UtcNow;
        // Create a summary object
        var summary = new DepositRewardSummary
        {
            DepositId = depositId,
            DepositAmount = deposit.Amount,
            DepositDate = deposit.CreatedDate,
            UserEmail = deposit.User.Email,
            UserFullName = $"{deposit.User.Name} {deposit.User.Surname}",
            RewardStatus = deposit.RewardStatus,
            ProcessDate = nowUtc,
            RzwBuyPrice = rzwBuyPrice,
            Rewards = []
        };

        while (currentUser.ReferrerId.HasValue && level < 10)
        {
            level++;

            var referrer = await _context.Users
                .FirstOrDefaultAsync(u => u.UserId == currentUser.ReferrerId.Value);

            if (referrer == null)
            {
                _logger.LogWarning("Referrer not found for user {UserId} at level {Level}", currentUser.UserId, level);
                break;
            }

            // Check if referrer has an active package
            var activePackage = await _context.UserPackages
                .Where(up => up.UserId == referrer.UserId && up.Status == UserPackageStatus.Active)
                .Include(up => up.Package) // Include the Package entity to get the name
                .OrderByDescending(up => up.PackageId) // Get the highest package if multiple are active
                .FirstOrDefaultAsync();

            if (activePackage != null)
            {
                // Add to the list of referrers
                referrers.Add((referrer, level, activePackage.PackageId));
            }

            currentUser = referrer;
        }

        if (referrers.Count == 0)
        {
            _logger.LogInformation("No referrers with active packages found for user {UserId}", depositUserId);
            return summary;
        }

        // Calculate potential rewards
        var potentialRewards = new List<RewardDetailViewModel>();

        foreach (var (referrer, referrerLevel, packageId) in referrers)
        {
            if (!packageId.HasValue)
                continue;

            // Get the reward percentage for this package and level
            var packagePercentage = await _context.PackageRewardPercentages
                .Include(p => p.Package) // Include the Package entity to get the name
                .FirstOrDefaultAsync(p => p.PackageId == packageId.Value && p.Level == referrerLevel);

            if (packagePercentage != null)
            {
                decimal tlPercentage = packagePercentage.TlPercentage;
                decimal rzwPercentage = packagePercentage.RzwPercentage;

                if (tlPercentage <= 0 && rzwPercentage <= 0)
                {
                    _logger.LogInformation("Both TL and RZW percentages for level {Level} in package {PackageId} are zero or negative: TL={TlPercentage}, RZW={RzwPercentage}",
                        referrerLevel, packageId, tlPercentage, rzwPercentage);
                    continue;
                }

                // Calculate TL reward amount directly
                var tlRewardAmount = depositAmount * (tlPercentage / 100);

                // Calculate RZW reward amount (convert TL to RZW based on current price)
                var rzwRewardAmount = depositAmount * (rzwPercentage / 100) / rzwBuyPrice;

                _logger.LogInformation("Calculating potential reward for referrer {UserId} at level {Level}: TL: {Amount} TL * {TlPercentage}% = {TlReward} TL, RZW: {Amount} TL * {RzwPercentage}% / {RzwBuyPrice} TL/RZW = {RzwReward} RZW",
                    referrer.UserId, referrerLevel, depositAmount, tlPercentage, tlRewardAmount, depositAmount, rzwPercentage, rzwBuyPrice, rzwRewardAmount);

                // Create reward preview
                var rewardPreview = new RewardDetailViewModel
                {
                    UserId = referrer.UserId,
                    UserEmail = referrer.Email,
                    UserFullName = $"{referrer.Name} {referrer.Surname}",
                    ReferredUserId = depositUserId,
                    ReferredUserEmail = depositUser.Email,
                    ReferredUserFullName = $"{depositUser.Name} {depositUser.Surname}",
                    Level = referrerLevel,
                    RzwAmount = rzwRewardAmount,
                    TlAmount = tlRewardAmount,
                    RzwPercentage = rzwPercentage,
                    TlPercentage = tlPercentage,
                    DepositAmount = depositAmount,
                    RzwBuyPrice = rzwBuyPrice,
                    RewardType = "DEPOSIT",
                    DepositDate = nowUtc,
                    PackageId = packageId.Value,
                    PackageName = packagePercentage.Package.Name
                };

                potentialRewards.Add(rewardPreview);
            }
            else
            {
                _logger.LogInformation("Referrer {UserId} at level {Level} has package {PackageId} but no reward percentage defined for this level",
                    referrer.UserId, referrerLevel, packageId);
            }
        }

        // Update summary
        summary.RewardedUsersCount = potentialRewards.Count;
        summary.TotalRzwDistributed = potentialRewards.Sum(r => r.RzwAmount);
        summary.TotalTlDistributed = potentialRewards.Sum(r => r.TlAmount);
        summary.Rewards = potentialRewards;

        return summary;
    }

    /// <summary>
    /// Recalculates rewards for a date range based on the new TL and RZW percentages
    /// </summary>
    /// <param name="startDate">Start date for recalculation</param>
    /// <param name="endDate">End date for recalculation</param>
    /// <returns>Number of rewards recalculated</returns>
    public async Task<int> RecalculateRewardsAsync(DateTime startDate, DateTime endDate)
    {
        // Get all rewards in the date range
        var rewards = await _context.ReferralRewards
            .Include(r => r.Package)
            .Where(r => r.DepositDate >= startDate && r.DepositDate <= endDate)
            .ToListAsync();

        if (rewards.Count == 0)
        {
            _logger.LogInformation("No rewards found in date range {StartDate} to {EndDate}", startDate, endDate);
            return 0;
        }

        _logger.LogInformation("Recalculating {Count} rewards from {StartDate} to {EndDate}", rewards.Count, startDate, endDate);

        int recalculatedCount = 0;

        foreach (var reward in rewards)
        {
            // Get the package reward percentage for this package and level
            var packagePercentage = await _context.PackageRewardPercentages
                .FirstOrDefaultAsync(p => p.PackageId == reward.PackageId && p.Level == reward.Level);

            if (packagePercentage == null)
            {
                _logger.LogWarning("Package percentage not found for package {PackageId} and level {Level}", reward.PackageId, reward.Level);
                continue;
            }

            // Get the TL and RZW percentages
            decimal tlPercentage = packagePercentage.TlPercentage;
            decimal rzwPercentage = packagePercentage.RzwPercentage;

            // Calculate the new TL and RZW amounts
            decimal tlAmount = reward.DepositAmount * (tlPercentage / 100);
            decimal rzwAmount = reward.DepositAmount * (rzwPercentage / 100) / reward.RzwPrice;

            // Update the reward
            reward.TlPercentage = tlPercentage;
            reward.RzwPercentage = rzwPercentage;
            reward.TlAmount = tlAmount;
            reward.RzwAmount = rzwAmount;
            reward.ModifiedDate = DateTime.UtcNow;

            // Get the user
            var user = await _context.Users.FindAsync(reward.UserId);
            if (user != null)
            {
                // For testing purposes, don't update the user's balance
                // user.Balance += tlAmount;
                user.ModDate = DateTime.UtcNow;

                // Create a balance transaction record
                var balanceTransaction = new BalanceTransaction
                {
                    IsActive = true,
                    UserId = reward.UserId,
                    Amount = tlAmount,
                    PreviousBalance = user.Balance - tlAmount,
                    NewBalance = user.Balance,
                    TransactionType = TransactionType.ReferralReward,
                    ReferenceId = reward.Id,
                    ReferenceType = "ReferralReward",
                    Description = $"Recalculated referral reward for deposit #{reward.DepositId} (Level {reward.Level})",
                    CreatedDate = DateTime.UtcNow
                };

                _context.BalanceTransactions.Add(balanceTransaction);

                _logger.LogInformation("Added {Amount} TL to balance of user {UserId} for recalculated reward", tlAmount, reward.UserId);
            }

            recalculatedCount++;
        }

        // Save changes
        await _context.SaveChangesAsync();

        _logger.LogInformation("Recalculated {Count} rewards", recalculatedCount);

        return recalculatedCount;
    }

    /// <summary>
    /// Counts the number of rewards in a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Number of rewards in the date range</returns>
    public async Task<int> CountRewardsInDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.ReferralRewards
            .Where(r => r.DepositDate >= startDate && r.DepositDate <= endDate)
            .CountAsync();
    }

    /// <summary>
    /// Calculates the total TL amount that would be distributed for rewards in a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Total TL amount</returns>
    public async Task<decimal> CalculateTotalTlAmountForDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var rewards = await _context.ReferralRewards
            .Include(r => r.Package)
            .Where(r => r.DepositDate >= startDate && r.DepositDate <= endDate)
            .ToListAsync();

        decimal totalTlAmount = 0;

        foreach (var reward in rewards)
        {
            var packagePercentage = await _context.PackageRewardPercentages
                .FirstOrDefaultAsync(p => p.PackageId == reward.PackageId && p.Level == reward.Level);

            if (packagePercentage == null)
            {
                continue;
            }

            decimal tlPercentage = packagePercentage.TlPercentage;
            decimal tlAmount = reward.DepositAmount * (tlPercentage / 100);

            totalTlAmount += tlAmount;
        }

        return totalTlAmount;
    }
    public async Task<decimal> CalculateTotalRzwAmountForDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        // Get RZW token information (ID and price) in a single query
        var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
        decimal rzwBuyPrice = rzwTokenInfo.BuyPrice;

        var rewards = await _context.ReferralRewards
            .Include(r => r.Package)
            .Where(r => r.DepositDate >= startDate && r.DepositDate <= endDate)
            .ToListAsync();

        decimal totalRzwAmount = 0;

        foreach (var reward in rewards)
        {
            var packagePercentage = await _context.PackageRewardPercentages
                .FirstOrDefaultAsync(p => p.PackageId == reward.PackageId && p.Level == reward.Level);

            if (packagePercentage == null)
            {
                continue;
            }

            decimal rzwPercentage = packagePercentage.RzwPercentage;
            decimal rzwAmount = reward.DepositAmount * (rzwPercentage / 100) / rzwBuyPrice;

            totalRzwAmount += rzwAmount;
        }

        return totalRzwAmount;
    }
    /// <summary>
    /// Counts the number of users affected by rewards in a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Number of affected users</returns>
    public async Task<int> CountAffectedUsersInDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.ReferralRewards
            .Where(r => r.DepositDate >= startDate && r.DepositDate <= endDate)
            .Select(r => r.UserId)
            .Distinct()
            .CountAsync();
    }


}

/// <summary>
/// Summary of referral rewards for a user
/// </summary>
public class ReferralRewardSummary
{
    public decimal TotalRzwRewards { get; set; }
    public decimal TotalTlRewards { get; set; }
    public Dictionary<int, decimal> RzwRewardsByLevel { get; set; } = [];
    public Dictionary<int, decimal> TlRewardsByLevel { get; set; } = [];
    public int TotalReferrals { get; set; }
    public Dictionary<int, int> ReferralsByLevel { get; set; } = [];
}
