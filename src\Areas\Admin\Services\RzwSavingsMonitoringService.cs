using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for monitoring RZW Savings system health and performance
/// </summary>
public class RzwSavingsMonitoringService
{
    private readonly AppDbContext _context;

    public RzwSavingsMonitoringService(AppDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Gets system health information
    /// </summary>
    /// <returns>System health data</returns>
    public async Task<SystemHealthInfo> GetSystemHealthAsync()
    {
        var today = DateTime.UtcNow.Date;
        
        var totalActiveAccounts = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Active)
            .CountAsync();

        var totalLockedRzw = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Active)
            .SumAsync(a => a.RzwAmount);

        var pendingInterestPayments = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Active &&
                       (a.LastInterestDate == null || a.LastInterestDate.Value.Date < today))
            .CountAsync();

        var maturedAccounts = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Active && a.MaturityDate <= DateTime.UtcNow)
            .CountAsync();

        var todayInterestPayments = await _context.RzwSavingsInterestPayments
            .Where(p => p.PaymentDate.Date == today)
            .SumAsync(p => p.RzwAmount);

        var todayInterestCount = await _context.RzwSavingsInterestPayments
            .Where(p => p.PaymentDate.Date == today)
            .CountAsync();

        return new SystemHealthInfo
        {
            TotalActiveAccounts = totalActiveAccounts,
            TotalLockedRzw = totalLockedRzw,
            PendingInterestPayments = pendingInterestPayments,
            MaturedAccounts = maturedAccounts,
            TodayInterestPayments = todayInterestPayments,
            TodayInterestCount = todayInterestCount,
            LastUpdateTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Gets performance metrics for the last 30 days
    /// </summary>
    /// <returns>Performance metrics</returns>
    public async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
    {
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);

        var newAccountsLast30Days = await _context.RzwSavingsAccounts
            .Where(a => a.CreatedDate >= thirtyDaysAgo)
            .CountAsync();

        var totalInterestPaidLast30Days = await _context.RzwSavingsInterestPayments
            .Where(p => p.PaymentDate >= thirtyDaysAgo)
            .SumAsync(p => p.RzwAmount);

        var earlyWithdrawalsLast30Days = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Withdrawn &&
                       a.ModifiedDate >= thirtyDaysAgo)
            .CountAsync();

        var maturedAccountsLast30Days = await _context.RzwSavingsAccounts
            .Where(a => a.Status == RzwSavingsStatus.Matured && 
                       a.ModifiedDate >= thirtyDaysAgo)
            .CountAsync();

        return new PerformanceMetrics
        {
            NewAccountsLast30Days = newAccountsLast30Days,
            TotalInterestPaidLast30Days = totalInterestPaidLast30Days,
            EarlyWithdrawalsLast30Days = earlyWithdrawalsLast30Days,
            MaturedAccountsLast30Days = maturedAccountsLast30Days,
            PeriodStart = thirtyDaysAgo,
            PeriodEnd = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Gets plan performance statistics
    /// </summary>
    /// <returns>Plan performance data</returns>
    public async Task<List<PlanPerformanceInfo>> GetPlanPerformanceAsync()
    {
        var planStats = await _context.RzwSavingsPlans
            .Select(p => new PlanPerformanceInfo
            {
                PlanId = p.Id,
                PlanName = p.Name,
                TermType = p.TermType,
                TermDuration = p.TermDuration,
                InterestRate = p.InterestRate,
                IsActive = p.IsActive,
                TotalAccounts = _context.RzwSavingsAccounts.Count(a => a.PlanId == p.Id),
                ActiveAccounts = _context.RzwSavingsAccounts.Count(a => a.PlanId == p.Id && a.Status == RzwSavingsStatus.Active),
                TotalLockedRzw = _context.RzwSavingsAccounts
                    .Where(a => a.PlanId == p.Id && a.Status == RzwSavingsStatus.Active)
                    .Sum(a => a.RzwAmount),
                TotalInterestPaid = _context.RzwSavingsInterestPayments
                    .Where(ip => ip.RzwSavingsAccount.PlanId == p.Id)
                    .Sum(ip => ip.RzwAmount)
            })
            .ToListAsync();

        return planStats;
    }
}

public class SystemHealthInfo
{
    public int TotalActiveAccounts { get; set; }
    public decimal TotalLockedRzw { get; set; }
    public int PendingInterestPayments { get; set; }
    public int MaturedAccounts { get; set; }
    public decimal TodayInterestPayments { get; set; }
    public int TodayInterestCount { get; set; }
    public DateTime LastUpdateTime { get; set; }
}

public class PerformanceMetrics
{
    public int NewAccountsLast30Days { get; set; }
    public decimal TotalInterestPaidLast30Days { get; set; }
    public int EarlyWithdrawalsLast30Days { get; set; }
    public int MaturedAccountsLast30Days { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class PlanPerformanceInfo
{
    public int PlanId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public string TermType { get; set; } = string.Empty;
    public int TermDuration { get; set; }
    public decimal InterestRate { get; set; }
    public bool IsActive { get; set; }
    public int TotalAccounts { get; set; }
    public int ActiveAccounts { get; set; }
    public decimal TotalLockedRzw { get; set; }
    public decimal TotalInterestPaid { get; set; }
}
