using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Test class to validate Phase 2 implementation
/// This class should be removed after testing is complete
/// </summary>
public class TestPhase2Implementation
{
    private readonly IWalletService _walletService;
    private readonly RzwBalanceManagementService _rzwBalanceService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly AppDbContext _context;

    public TestPhase2Implementation(
        IWalletService walletService,
        RzwBalanceManagementService rzwBalanceService,
        ITokenPriceService tokenPriceService,
        AppDbContext context)
    {
        _walletService = walletService;
        _rzwBalanceService = rzwBalanceService;
        _tokenPriceService = tokenPriceService;
        _context = context;
    }

    /// <summary>
    /// Test basic wallet operations
    /// </summary>
    public async Task<string> TestBasicWalletOperationsAsync()
    {
        try
        {
            // Get a test user (first user in database)
            var testUser = await _context.Users.FirstOrDefaultAsync();
            if (testUser == null)
            {
                return "❌ No test user found in database";
            }

            var userId = testUser.UserId;
            var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();

            // Test 1: Get initial balance
            var initialBalance = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenInfo.TokenId);

            // Test 2: Add available balance
            await _walletService.AddAvailableBalanceAsync(userId, rzwTokenInfo, 100m, TradeType.Buy);
            var balanceAfterAdd = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenInfo.TokenId);

            if (balanceAfterAdd != initialBalance + 100m)
            {
                return $"❌ Add balance failed. Expected: {initialBalance + 100m}, Got: {balanceAfterAdd}";
            }

            // Test 3: Lock balance
            var lockSuccess = await _walletService.LockBalanceAsync(userId, rzwTokenInfo, 50m);
            if (!lockSuccess)
            {
                return "❌ Lock balance failed";
            }

            var availableAfterLock = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenInfo.TokenId);
            var lockedAfterLock = await _walletService.GetUserLockedBalanceAsync(userId, rzwTokenInfo.TokenId);

            if (availableAfterLock != balanceAfterAdd - 50m)
            {
                return $"❌ Available balance after lock incorrect. Expected: {balanceAfterAdd - 50m}, Got: {availableAfterLock}";
            }

            // Test 4: Get total balance
            var totalBalance = await _walletService.GetUserTotalBalanceAsync(userId, rzwTokenInfo.TokenId);
            if (totalBalance != availableAfterLock + lockedAfterLock)
            {
                return $"❌ Total balance calculation incorrect. Expected: {availableAfterLock + lockedAfterLock}, Got: {totalBalance}";
            }

            // Test 5: Get balance info
            var balanceInfo = await _walletService.GetWalletBalanceInfoAsync(userId, rzwTokenInfo.TokenId);
            if (balanceInfo.AvailableBalance != availableAfterLock || balanceInfo.LockedBalance != lockedAfterLock)
            {
                return "❌ Balance info incorrect";
            }

            // Test 6: Unlock balance
            var unlockSuccess = await _walletService.UnlockBalanceAsync(userId, rzwTokenInfo, 25m);
            if (!unlockSuccess)
            {
                return "❌ Unlock balance failed";
            }

            return "✅ All basic wallet operations tests passed!";
        }
        catch (Exception ex)
        {
            return $"❌ Test failed with exception: {ex.Message}";
        }
    }

    /// <summary>
    /// Test RZW balance management operations
    /// </summary>
    public async Task<string> TestRzwBalanceManagementAsync()
    {
        try
        {
            // Get a test user
            var testUser = await _context.Users.FirstOrDefaultAsync();
            if (testUser == null)
            {
                return "❌ No test user found in database";
            }

            var userId = testUser.UserId;

            // Test 1: Get RZW balance info
            var rzwBalanceInfo = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId);
            if (rzwBalanceInfo.UserId != userId)
            {
                return "❌ RZW balance info user ID mismatch";
            }

            // Test 2: Check sufficient balance
            var hasSufficientBalance = await _rzwBalanceService.HasSufficientAvailableRzwAsync(userId, 10m);

            // Test 3: Lock RZW for savings (if sufficient balance)
            if (hasSufficientBalance)
            {
                var lockSuccess = await _rzwBalanceService.LockRzwForSavingsAsync(userId, 10m, "Test savings lock");
                if (!lockSuccess)
                {
                    return "❌ RZW savings lock failed";
                }

                // Test 4: Unlock RZW from savings
                var unlockSuccess = await _rzwBalanceService.UnlockRzwFromSavingsAsync(userId, 5m, "Test savings unlock");
                if (!unlockSuccess)
                {
                    return "❌ RZW savings unlock failed";
                }
            }

            // Test 5: Add RZW interest
            var wallet = await _rzwBalanceService.AddRzwInterestAsync(userId, 1m, "Test interest payment");
            if (wallet == null)
            {
                return "❌ RZW interest addition failed";
            }

            return "✅ All RZW balance management tests passed!";
        }
        catch (Exception ex)
        {
            return $"❌ RZW test failed with exception: {ex.Message}";
        }
    }

    /// <summary>
    /// Test backward compatibility
    /// </summary>
    public async Task<string> TestBackwardCompatibilityAsync()
    {
        try
        {
            var testUser = await _context.Users.FirstOrDefaultAsync();
            if (testUser == null)
            {
                return "❌ No test user found in database";
            }

            var userId = testUser.UserId;
            var rzwTokenId = await _tokenPriceService.GetRzwTokenIdAsync();

            // Test obsolete methods still work
#pragma warning disable CS0618 // Type or member is obsolete
            var oldBalance = await _walletService.GetUserBalanceAsync(userId, rzwTokenId);
            var newBalance = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenId);
#pragma warning restore CS0618 // Type or member is obsolete

            if (oldBalance != newBalance)
            {
                return $"❌ Backward compatibility failed. Old method: {oldBalance}, New method: {newBalance}";
            }

            return "✅ Backward compatibility test passed!";
        }
        catch (Exception ex)
        {
            return $"❌ Backward compatibility test failed with exception: {ex.Message}";
        }
    }

    /// <summary>
    /// Run all tests
    /// </summary>
    public async Task<List<string>> RunAllTestsAsync()
    {
        var results = new List<string>();

        results.Add("🧪 Testing Phase 2 Implementation...");
        results.Add("");

        results.Add("1. Testing Basic Wallet Operations:");
        results.Add(await TestBasicWalletOperationsAsync());
        results.Add("");

        results.Add("2. Testing RZW Balance Management:");
        results.Add(await TestRzwBalanceManagementAsync());
        results.Add("");

        results.Add("3. Testing Backward Compatibility:");
        results.Add(await TestBackwardCompatibilityAsync());
        results.Add("");

        results.Add("🏁 Phase 2 testing completed!");

        return results;
    }
}
