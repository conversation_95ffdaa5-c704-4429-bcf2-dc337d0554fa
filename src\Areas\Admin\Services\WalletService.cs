using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;
using System.Data;

namespace RazeWinComTr.Areas.Admin.Services;

public class WalletService : IWalletService
{
    private readonly AppDbContext _context;
    private readonly TradeService _tradeService;
    private readonly ITokenPriceService _tokenPriceService;

    public WalletService(AppDbContext context, TradeService tradeService, ITokenPriceService tokenPriceService)
    {
        _context = context;
        _tradeService = tradeService;
        _tokenPriceService = tokenPriceService;
    }

    public async Task<Wallet?> GetByIdAsync(int id)
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .ToListAsync();
    }

    public async Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => (double)w.Balance) // Cast decimal to double for SQLite
            .Take(topN)
            .ToListAsync();
    }

    public async Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == coinId);
    }

    public async Task<List<WalletViewModel>> GetListAsync()
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin != null ? w.Coin.PairCode : string.Empty,
                Balance = w.Balance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }

    public async Task<Wallet> CreateAsync(Wallet wallet, IDbContextTransaction? existingTransaction = null)
    {
        var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();
        _context.Wallets.Add(wallet);
        await _context.SaveChangesAsync();
        if (existingTransaction == null)
        {
            await transaction.CommitAsync();
        }
        return wallet;
    }

    public async Task UpdateAsync(Wallet wallet)
    {
        wallet.ModifiedDate = DateTime.UtcNow;
        _context.Entry(wallet).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var wallet = await _context.Wallets.FindAsync(id);
        if (wallet != null)
        {
            _context.Wallets.Remove(wallet);
            await _context.SaveChangesAsync();
        }
    }

    // Helper methods for transaction-safe operations
    private Wallet CreateWalletWithoutSaveAsync(Wallet wallet)
    {
        _context.Wallets.Add(wallet);
        return wallet;
    }

    private void UpdateWalletWithoutSaveAsync(Wallet wallet)
    {
        wallet.ModifiedDate = DateTime.UtcNow;
        _context.Entry(wallet).State = EntityState.Modified;
    }

    private async Task CreateTradeRecordAsync(int userId, RzwTokenInfo rzwTokenInfo, TradeType tradeType, decimal coinAmount, decimal tryAmount,
        decimal previousBalance, decimal newBalance, decimal previousLockedCoinBalance, decimal newLockedCoinBalance, IDbContextTransaction existingTransaction)
    {
        var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();
        var trade = new Trade
        {
            UserId = userId,
            CoinId = rzwTokenInfo.TokenId,
            Type = tradeType,
            CoinAmount = coinAmount,
            TryAmount = tryAmount,
            CoinRate = rzwTokenInfo.BuyPrice,
            PreviousBalance = previousBalance,
            NewBalance = newBalance,
            PreviousCoinBalance = 0, // Not applicable for wallet operations
            NewCoinBalance = 0, // Not applicable for wallet operations
            PreviousWalletBalance = previousBalance + previousLockedCoinBalance,
            NewWalletBalance = newBalance + newLockedCoinBalance,
            CreatedDate = DateTime.UtcNow,
            IsActive = true,
        };
        await _tradeService.CreateAsync(trade, transaction);
        if (existingTransaction == null)
        {
            await transaction.CommitAsync();
        }
        //_context.Trades.Add(trade);
    }

    // BACKWARD COMPATIBILITY METHODS - Obsolete
    [Obsolete("Use GetUserAvailableBalanceAsync instead")]
    public async Task<decimal> GetUserBalanceAsync(int userId, int coinId)
    {
        return await GetUserAvailableBalanceAsync(userId, coinId);
    }

    [Obsolete("Use AddAvailableBalanceAsync instead")]
    public async Task<Wallet> AddBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await AddAvailableBalanceAsync(userId, rzwTokenInfo, amount);
    }

    [Obsolete("Use DeductAvailableBalanceAsync instead")]
    public async Task<bool> DeductBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await DeductAvailableBalanceAsync(userId, rzwTokenInfo, amount);
    }

    [Obsolete("Use AddToAvailableBalanceAsync instead")]
    public async Task<Wallet> AddToWalletAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await AddToAvailableBalanceAsync(userId, rzwTokenInfo, amount);
    }

    // NEW METHODS - Available Balance
    public async Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.Balance ?? 0;
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await AddAvailableBalanceAsync(userId, rzwTokenInfo, amount, transaction);
            await transaction.CommitAsync();
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction existingTransaction)
    {
        var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);
        var previousBalance = wallet?.Balance ?? 0;

        if (wallet == null)
        {
            // Create new wallet if it doesn't exist
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                LockedBalance = 0,
                CreatedDate = DateTime.UtcNow
            };
            wallet = CreateWalletWithoutSaveAsync(wallet);
        }
        else
        {
            // Update existing wallet
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            UpdateWalletWithoutSaveAsync(wallet);
        }

        // Create audit trail
        CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Buy, amount, 0,
            previousBalance, wallet.Balance, wallet.LockedBalance, wallet.LockedBalance, transaction);

        await _context.SaveChangesAsync();
        return wallet;
    }

    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await DeductAvailableBalanceAsync(userId, rzwTokenInfo, amount, transaction);
            if (result)
            {
                await transaction.CommitAsync();
            }
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction existingTransaction)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null || wallet.Balance < amount)
        {
            return false; // Insufficient balance
        }

        var previousBalance = wallet.Balance;
        wallet.Balance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        UpdateWalletWithoutSaveAsync(wallet);

        // Create audit trail
        CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Sell, -amount, 0,
            previousBalance, wallet.Balance, wallet.LockedBalance, wallet.LockedBalance, existingTransaction);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<Wallet> AddToAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await AddAvailableBalanceAsync(userId, rzwTokenInfo, amount);
    }

    // NEW METHODS - Locked Balance
    public async Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.LockedBalance ?? 0;
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await LockBalanceAsync(userId, rzwTokenInfo, amount, transaction);
            if (result)
            {
                await transaction.CommitAsync();
            }
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction existingTransaction = null)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null || wallet.Balance < amount)
        {
            return false; // Insufficient available balance
        }

        var previousAvailableBalance = wallet.Balance;
        var previousLockedBalance = wallet.LockedBalance;

        // Move from available to locked
        wallet.Balance -= amount;
        wallet.LockedBalance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        UpdateWalletWithoutSaveAsync(wallet);

        // Create audit trail
        CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Buy, -amount, 0,
            previousAvailableBalance, wallet.Balance, previousLockedBalance, wallet.LockedBalance, existingTransaction);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await UnlockBalanceAsync(userId, rzwTokenInfo, amount, transaction);
            if (result)
            {
                await transaction.CommitAsync();
            }
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction existingTransaction)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null || wallet.LockedBalance < amount)
        {
            return false; // Insufficient locked balance
        }

        var previousAvailableBalance = wallet.Balance;
        var previousLockedBalance = wallet.LockedBalance;

        // Move from locked to available
        wallet.LockedBalance -= amount;
        wallet.Balance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        UpdateWalletWithoutSaveAsync(wallet);

        // Create audit trail
        CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Sell, amount, 0,
            previousAvailableBalance, wallet.Balance, previousLockedBalance, wallet.LockedBalance, existingTransaction);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<Wallet> AddLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction? existingTransaction = null)
    {
        using var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();
        try
        {
            var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);
            var previousLockedBalance = wallet?.LockedBalance ?? 0;

            if (wallet == null)
            {
                // Create new wallet if it doesn't exist
                wallet = new Wallet
                {
                    UserId = userId,
                    CoinId = rzwTokenInfo.TokenId,
                    Balance = 0,
                    LockedBalance = amount,
                    CreatedDate = DateTime.UtcNow
                };
                wallet = CreateWalletWithoutSaveAsync(wallet);
            }
            else
            {
                // Update existing wallet
                wallet.LockedBalance += amount;
                wallet.ModifiedDate = DateTime.UtcNow;
                UpdateWalletWithoutSaveAsync(wallet);
            }

            // Create audit trail
            CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Buy, 0, 0,
                wallet.Balance, wallet.Balance, previousLockedBalance, wallet.LockedBalance, transaction);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return wallet;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<bool> DeductLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction? existingTransaction = null)
    {
        using var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();
        try
        {
            var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

            if (wallet == null || wallet.LockedBalance < amount)
            {
                return false; // Insufficient locked balance
            }

            var previousLockedBalance = wallet.LockedBalance;
            wallet.LockedBalance -= amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            UpdateWalletWithoutSaveAsync(wallet);

            // Create audit trail
            CreateTradeRecordAsync(userId, rzwTokenInfo, TradeType.Sell, 0, 0,
                wallet.Balance, wallet.Balance, previousLockedBalance, wallet.LockedBalance, transaction);

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    // NEW METHODS - Total Balance
    public async Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.TotalBalance ?? 0;
    }

    // NEW METHODS - Balance Info
    public async Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId)
    {
        var wallet = await _context.Wallets
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == coinId);

        if (wallet == null)
        {
            // Return empty balance info if wallet doesn't exist
            var coin = await _context.Markets.FindAsync(coinId);
            return new WalletBalanceInfo
            {
                UserId = userId,
                CoinId = coinId,
                CoinCode = coin?.PairCode ?? string.Empty,
                CoinName = coin?.Name ?? string.Empty,
                IconUrl = coin?.IconUrl ?? string.Empty,
                AvailableBalance = 0,
                LockedBalance = 0
            };
        }

        var balanceInfo = new WalletBalanceInfo
        {
            UserId = userId,
            CoinId = coinId,
            CoinCode = wallet.Coin?.PairCode ?? string.Empty,
            CoinName = wallet.Coin?.Name ?? string.Empty,
            IconUrl = wallet.Coin?.IconUrl ?? string.Empty,
            AvailableBalance = wallet.Balance,
            LockedBalance = wallet.LockedBalance
        };

        // Add RZW-specific information if this is the RZW token
        if (wallet.Coin?.PairCode == "RZW" || wallet.Coin?.Coin == "RZW")
        {
            // TODO: Add savings-specific locked amounts when savings system is implemented
            balanceInfo.LockedInSavings = 0; // Will be populated in future phases
            balanceInfo.SavingsLocks = new List<SavingsLockInfo>(); // Will be populated in future phases
        }

        return balanceInfo;
    }

    public async Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId)
    {
        var wallets = await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .ToListAsync();

        var balanceInfoList = new List<WalletBalanceInfo>();

        foreach (var wallet in wallets)
        {
            var balanceInfo = new WalletBalanceInfo
            {
                UserId = userId,
                CoinId = wallet.CoinId,
                CoinCode = wallet.Coin?.PairCode ?? string.Empty,
                CoinName = wallet.Coin?.Name ?? string.Empty,
                IconUrl = wallet.Coin?.IconUrl ?? string.Empty,
                AvailableBalance = wallet.Balance,
                LockedBalance = wallet.LockedBalance
            };

            // Add RZW-specific information if this is the RZW token
            if (wallet.Coin?.PairCode == "RZW" || wallet.Coin?.Coin == "RZW")
            {
                // TODO: Add savings-specific locked amounts when savings system is implemented
                balanceInfo.LockedInSavings = 0; // Will be populated in future phases
                balanceInfo.SavingsLocks = new List<SavingsLockInfo>(); // Will be populated in future phases
            }

            balanceInfoList.Add(balanceInfo);
        }

        return balanceInfoList;
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await AddAvailableBalanceAsync(userId, rzwTokenInfo, amount, tradeType, transaction);
            await transaction.CommitAsync();
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, IDbContextTransaction existingTransaction)
    {
        // Only create a new transaction if one wasn't provided
        var useExistingTransaction = existingTransaction != null;
        var transaction = existingTransaction ?? await _context.Database.BeginTransactionAsync();

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);
        var previousBalance = wallet?.Balance ?? 0;

        if (wallet == null)
        {
            // Create new wallet if it doesn't exist
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                LockedBalance = 0,
                CreatedDate = DateTime.UtcNow
            };
            wallet = CreateWalletWithoutSaveAsync(wallet);
        }
        else
        {
            // Update existing wallet
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            UpdateWalletWithoutSaveAsync(wallet);
        }

        // Create audit trail
        CreateTradeRecordAsync(userId, rzwTokenInfo, tradeType, amount, 0,
            previousBalance, wallet.Balance, wallet.LockedBalance, wallet.LockedBalance, transaction);

        await _context.SaveChangesAsync();
        return wallet;
    }
}
