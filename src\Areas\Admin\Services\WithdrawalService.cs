using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Withdrawal;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.Admin.Services;

public class WithdrawalService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly BalanceTransactionService _balanceTransactionService;

    public WithdrawalService(
        IStringLocalizer<SharedResource> localizer,
        AppDbContext context,
        BalanceTransactionService balanceTransactionService)
    {
        _localizer = localizer;
        _context = context;
        _balanceTransactionService = balanceTransactionService;
    }

    public async Task<Withdrawal?> GetByIdAsync(int id)
    {
        return await _context.Withdrawals
            .Include(w => w.User)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<List<WithdrawalViewModel>> GetListAsync()
    {
        return await _context.Withdrawals
            .Include(w => w.User)
            .Select(w => new WithdrawalViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                FullName = w.FullName,
                Email = w.Email,
                Balance = w.Balance,
                WithdrawalAmount = w.WithdrawalAmount,
                AccountHolder = w.AccountHolder,
                Iban = w.Iban,
                Status = w.Status,
                CreatedDate = w.CreatedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<WithdrawalViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.Withdrawals
            .Where(w => w.UserId == userId)
            .Select(w => new WithdrawalViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                FullName = w.FullName,
                Email = w.Email,
                Balance = w.Balance,
                WithdrawalAmount = w.WithdrawalAmount,
                AccountHolder = w.AccountHolder,
                Iban = w.Iban,
                Status = w.Status,
                CreatedDate = w.CreatedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Withdrawals.FindAsync(id);
        if (entity != null)
        {
            _context.Withdrawals.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Withdrawal> CreateAsync(Withdrawal withdrawal)
    {
        _context.Withdrawals.Add(withdrawal);
        await _context.SaveChangesAsync();
        return withdrawal;
    }

    public async Task UpdateAsync(Withdrawal withdrawal)
    {
        _context.Entry(withdrawal).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task<bool> UpdateWithdrawalStatusAsync(int withdrawalId, WithdrawalStatus newStatus)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var withdrawal = await _context.Withdrawals
                .Include(w => w.User)
                .FirstOrDefaultAsync(w => w.Id == withdrawalId);

            if (withdrawal == null)
                return false;

            var previousStatus = withdrawal.Status;

            // If status hasn't changed, just return true
            if (previousStatus == newStatus)
                return true;

            // Update withdrawal status
            withdrawal.Status = newStatus;

            // If changing from pending/rejected to approved
            if (newStatus == WithdrawalStatus.Approved && previousStatus != WithdrawalStatus.Approved)
            {
                // Record the balance transaction and update user's TRY balance
                await _balanceTransactionService.RecordTransactionAsync(
                   userId: withdrawal.UserId,
                   transactionType: TransactionType.Withdrawal,
                   amount: withdrawal.WithdrawalAmount,
                   description: _localizer["Withdrawal: {0}", withdrawal.Iban].Value,
                   referenceId: withdrawal.Id,
                   referenceType: BalanceTransactionReferenceTypes.Withdrawal,
                   existingTransaction: transaction
                );
            }
            // If changing from approved to pending/rejected
            else if (newStatus != WithdrawalStatus.Approved && previousStatus == WithdrawalStatus.Approved)
            {
                // Record the balance transaction and update user's TRY balance
                await _balanceTransactionService.RecordTransactionAsync(
                   userId: withdrawal.UserId,
                   transactionType: TransactionType.Deposit,
                   amount: withdrawal.WithdrawalAmount,
                   description: _localizer["Withdrawal reversal: {0}", withdrawal.Iban].Value,
                   referenceId: withdrawal.Id,
                   referenceType: BalanceTransactionReferenceTypes.Withdrawal, //BalanceTransactionReferenceTypes.WithdrawalReversal,
                   existingTransaction: transaction
                );
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
