namespace RazeWinComTr.Models;

/// <summary>
/// RZW-specific balance information for a user
/// </summary>
public class RzwBalanceInfo
{
    /// <summary>
    /// The user ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// Available RZW balance (can be used for trading/savings)
    /// </summary>
    public decimal AvailableRzw { get; set; }
    
    /// <summary>
    /// Locked RZW balance (cannot be used for trading)
    /// </summary>
    public decimal LockedRzw { get; set; }
    
    /// <summary>
    /// Total RZW balance (Available + Locked)
    /// </summary>
    public decimal TotalRzw => AvailableRzw + LockedRzw;
    
    /// <summary>
    /// Amount of RZW tokens locked in savings accounts (subset of LockedRzw)
    /// </summary>
    public decimal LockedInSavings { get; set; }
    
    /// <summary>
    /// Number of active savings accounts
    /// </summary>
    public int ActiveSavingsCount { get; set; }
    
    /// <summary>
    /// List of individual savings locks
    /// </summary>
    public List<SavingsLockInfo> SavingsLocks { get; set; } = new();
    
    // Computed properties
    
    /// <summary>
    /// Whether the user has any RZW balance
    /// </summary>
    public bool HasRzwBalance => TotalRzw > 0;
    
    /// <summary>
    /// Whether the user has any available RZW for new savings
    /// </summary>
    public bool HasAvailableRzw => AvailableRzw > 0;
    
    /// <summary>
    /// Whether the user has any locked RZW
    /// </summary>
    public bool HasLockedRzw => LockedRzw > 0;
    
    /// <summary>
    /// Whether the user has any savings accounts
    /// </summary>
    public bool HasSavingsAccounts => ActiveSavingsCount > 0;
    
    /// <summary>
    /// Amount of RZW locked for other purposes (not savings)
    /// </summary>
    public decimal LockedForOtherPurposes => LockedRzw - LockedInSavings;
}
