namespace RazeWinComTr.Models;

/// <summary>
/// Information about a specific savings lock for RZW tokens
/// </summary>
public class SavingsLockInfo
{
    /// <summary>
    /// The ID of the savings account that created this lock
    /// </summary>
    public int SavingsAccountId { get; set; }
    
    /// <summary>
    /// The amount of RZW tokens locked in this savings account
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    /// The maturity date when the savings account can be withdrawn
    /// </summary>
    public DateTime MaturityDate { get; set; }
    
    /// <summary>
    /// The name of the savings plan (e.g., "30 Days", "90 Days", etc.)
    /// </summary>
    public string PlanName { get; set; } = string.Empty;
    
    /// <summary>
    /// The annual interest rate for this savings plan (as percentage, e.g., 12.5 for 12.5%)
    /// </summary>
    public decimal InterestRate { get; set; }
    
    /// <summary>
    /// Number of days remaining until maturity
    /// </summary>
    public int DaysRemaining => (MaturityDate - DateTime.UtcNow).Days;
    
    /// <summary>
    /// Whether this savings account has matured and can be withdrawn
    /// </summary>
    public bool IsMatured => DateTime.UtcNow >= MaturityDate;
}
