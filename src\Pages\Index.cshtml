﻿@page
@model IndexModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = "Home page";
}


<div class="fw homeSliderAll">
    <div class="fw homeSlider">
        <div class="item">
            <div class="fw homeSliderItem cover"
                 style="background-image:url(/public/front/fxyatirim/assets/images/slider1-notext.jpg); display: flex; align-items: center; min-height: 560px;">
                <div class="container">
                    <div class="fw homePreview first-slider-preview" style="padding-left: 200px;margin-bottom: 0px;">
                        <ul>
                            <li class="homeSliderLogo wow zoomIn" data-wow-delay="0.3s" style="text-align: left;">
                                <img src="/public/image/GbErZcuBDF.png" alt="RAZEWIN Logo"/>
                            </li>
                            <li class="spacingTitle wow zoomIn"
                                data-wow-delay="0.4s" style="text-align: center; font-size: 22px; font-weight: 600; margin-bottom: 5px;">@Localizer["RAZEWIN TRAVEL EARNING APP"]</li>

                            <li class="wow zoomIn" data-wow-delay="0.5s" style="margin-bottom: 0; text-align: center; line-height: 1;">
                                <span class="discountX" style="font-size: 48px; font-weight: 700; color: #f1395c;">100.000+</span>
                            </li>
                            <li class="discount-subtitle wow zoomIn" data-wow-delay="0.5s" style="margin-bottom: 15px; text-align: center;">
                                <span style="font-size: 28px; font-weight: 600; color: #fff;">@Localizer["USERS EXCEEDED"]</span>
                            </li>
                            <li class="homeSliderText wow zoomIn" data-wow-delay="0.6s"
                                style="font-weight: 600; text-align: center; margin-bottom: 10px; font-size: 12px;">@Localizer["Meet Our Global User Network!"]</li>
                            <li class="appButtons wow zoomIn" data-wow-delay="0.9s" style="display: flex; flex-direction: row; gap: 15px; justify-content: center; margin-top: 10px;">
                                <div style="display: inline-block;">
                                    <a href="https://play.google.com/store/apps/details?id=com.razewin&gl=TR&pli=1" target="_blank" class="appStoreBtn googlePlay" style="display: inline-flex;">
                                        <i class="flaticon-google-play"></i>
                                        <span>Google Play</span>
                                    </a>
                                </div>
                                <div style="display: inline-block;">
                                    <a href="https://apps.apple.com/tr/app/razewin/id6569241832?l=tr" target="_blank" class="appStoreBtn appStore" style="display: inline-flex;">
                                        <i class="flaticon-apple"></i>
                                        <span>App Store</span>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="fw homePreviewAllAfter"></div>
            </div>
        </div>

        <div class="item">
            <div class="fw homeSliderItem cover"
                 style="background-image:url(/public/front/fxyatirim/assets/images/slider-2.jpg); display: flex; align-items: center; min-height: 560px;">
                <div class="container">
                    <div class="fw homePreview" style="padding-left: 200px; padding-right: 360px;">
                        <ul style="padding-right: 0;">
                            <li class="homeSliderLogo wow zoomIn" data-wow-delay="0.3s" style="text-align: center;">
                                <img src="/public/image/GbErZcuBDF.png" alt="RAZEWIN Logo"/>
                            </li>
                            <li class="spacingTitle wow zoomIn"
                                data-wow-delay="0.4s" style="text-align: center; font-size: 22px; font-weight: 600; margin-bottom: 5px;">RAZEWIN:</li>
                            <li class="homeSliderTitle wow zoomIn" data-wow-delay="0.6s"
                                style="font-weight: 700; text-align: center;">@Localizer["Future Investment Begins"]</li>
                            <li class="desc wow zoomIn" data-wow-delay="0.9s" style="text-align: center;">
                                @Localizer["Blockchain Powered Trading Experience"]
                            </li>
                            <li class="desc wow zoomIn" data-wow-delay="1.0s" style="text-align: center; font-weight: 600;">
                                @Localizer["Secure Fast Innovative"]
                            </li>
                            @if (!(User.Identity?.IsAuthenticated ?? false))
                            {
                                <li class="link wow zoomIn" data-wow-delay="1.2s" style="text-align: center; margin-top: 20px;">
                                    <a href="register" class="simpleButton orangeX">@Localizer["Start Investment Now"]<i
                                            class="flaticon-arrow-pointing-to-right"></i></a>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
                <div class="fw homePreviewAllAfter"></div>
            </div>
        </div>

        <div class="item">
            <div class="fw homeSliderItem cover" style="background-image:url(/wallpaperaccess.com/full/395437.jpg)">
                <div class="container">
                    <div class="fw homePreview">
                        <ul>
                            <li class="homeSliderLogo wow zoomIn" data-wow-delay="0.6s">
                                <img src="public/image/GbErZcuBDF.png"/>
                            </li>
                            <li class="homeSliderTitle wow zoomIn" data-wow-delay="0.9s"></li>
                            <li class="desc wow zoomIn" data-wow-delay="0.9s">
                                @Localizer["Rich Investment Variety"]
                            </li>
                            @if (!(User.Identity?.IsAuthenticated ?? false))
                            {
                                <li class="link twiceLink wow zoomIn" data-wow-delay="1.2s">
                                    <a href="login" class="simpleButton orangeX">@Localizer["Login"]<i
                                            class="flaticon-window"></i></a>
                                    <a href="register" class="simpleButton">@Localizer["Create Account"]<i
                                            class="flaticon-add"></i></a>
                                </li>
                            }
                        </ul>
                        <li class="img wow fadeInUp center" data-wow-delay="1.5s" style="height:339px"><img
                                src="public/front/fxyatirim/assets/images/slider2-img.png"/></li>
                    </div>
                </div>
                <div class="fw homePreviewAllAfter"></div>
            </div>
        </div>

        <div class="item">
            <div class="fw homeSliderItem cover"
                 style="background-image:url(/public/front/fxyatirim/assets/images/slider5.png)">
                <div class="container">
                    <div class="fw homePreview">
                        <ul style="padding-right: inherit;">
                            @* <li class="spacingTitle wow zoomIn"
                                data-wow-delay="0.3s">RAZEWIN @Localizer["Drive to Earn"]</li> *@
                            <li class="homeSliderTitle wow zoomIn" data-wow-delay="0.6s"
                                style="font-weight: 700; ">@Localizer["Earn While Driving"]</li>
                            <li class="desc wow zoomIn" data-wow-delay="0.9s">
                                @Localizer["Download Razewin App and Start Earning RZW Tokens While Traveling"]
                            </li>
                            <li class="img wow fadeInUp" data-wow-delay="1.5s">
                                <img src="public/front/fxyatirim/assets/images/drive-to-earn-car.png"/>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="fw homePreviewAllAfter"></div>
            </div>
        </div>

        <div class="item">
            <div class="fw homeSliderItem cover"
                 style="background-image:url(/public/front/fxyatirim/assets/images/slider5.png)">
                <div class="container">
                    <div class="fw homePreview">
                        <ul>
                            <li class="spacingTitle wow zoomIn"
                                data-wow-delay="0.3s">RAZEWIN @Localizer["Fast Withdrawals"]</li>
                            <li class="discount wow zoomIn" data-wow-delay="0.3s" style="margin-bottom: 0px;"><span
                                    class="discountX blueX" style="font-size: 30px; font-weight: 700">7/24</span></li>
                            <li class="homeSliderTitle wow zoomIn" data-wow-delay="0.6s"
                                style="font-weight: 700; ">@Localizer["Jet Speed Withdrawal"]</li>
                            <li class="desc wow zoomIn" data-wow-delay="0.9s">
                                @Localizer["No limits, no waiting at Razewin! Experience the difference with jet-speed deposit and withdrawal operations, including weekends."]
                            </li>
                            <li class="img wow fadeInUp" data-wow-delay="1.5s">
                                <img src="public/front/fxyatirim/assets/images/slider5-img.png"/>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="fw homePreviewAllAfter"></div>
            </div>
        </div>
    </div>
</div>

<!-- Info Panel Section -->
<div class="fw infoPanelSection">
    <div class="container">
        <div class="fw infoPanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="infoPanelContent">
                        <h2>@Localizer["Welcome to RazeWin"]</h2>
                        <p>@Localizer["Your trusted partner for cryptocurrency investments. Start trading today and experience the difference."]</p>
                        <div class="infoPanelStats">
                            <div class="statItem">
                                <span class="statNumber">24/7</span>
                                <span class="statLabel">@Localizer["Support"]</span>
                            </div>
                            <div class="statItem">
                                <span class="statNumber">100+</span>
                                <span class="statLabel">@Localizer["Cryptocurrencies"]</span>
                            </div>
                            <div class="statItem">
                                <span class="statNumber">0.1%</span>
                                <span class="statLabel">@Localizer["Trading Fee"]</span>
                            </div>
                        </div>
                        <a href="/register" class="infoPanelButton">@Localizer["Start Trading Now"]</a>
                        <a href="/EarnMoney" class="infoPanelButton earnButton">@Localizer["Earn Money"]</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="infoPanelImage">
                        <img src="/images/info-panel2.png" alt="RazeWin Trading Platform" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Info Panel Section -->

<div class="container">
    <div class="fw homePreviewBot blueX">
        <div class="homePreviewBotTab">
            <a href="login">
                <ul class="sul wow zoomIn">
                    <li class="img"><i class="textX blueX flaticon-window"></i></li>
                    <li class="title">@Localizer["Login Now"]</li>
                    <li class="desc">
                        Hesabınızı Dünya'da 1 numaralı aracı kurum olan Razewin'e taşıyarak sınırsız
                        ayrıcalıklardan yararlanın!
                    </li>
                </ul>
            </a>
        </div>
        <div class="homePreviewBotTab">
            <a href="register">
                <ul class="sul wow zoomIn">
                    <li class="img"><i class="textX greenX flaticon-user-3"></i></li>
                    <li class="title">@Localizer["Create Account"]</li>
                    <li class="desc">Razewin'in rekabetçi spread oranları ve hızlı işlem özelliği ile güvenle işlem
                        yapın!
                    </li>
                </ul>
            </a>
        </div>
        <div class="homePreviewBotTab">
            <a href="register">
                <ul class="sul wow zoomIn">
                    <li class="img"><i class="textX orangeX flaticon-arrow"></i></li>
                    <li class="title">@Localizer["Transfer Your Account"]</li>
                    <li class="desc">
                        Hesabınızı Dünya'da 1 numaralı aracı kurum olan Razewin'e taşıyarak sınırsız
                        ayrıcalıklardan yararlanın!
                    </li>
                </ul>
            </a>
        </div>
    </div>
</div>


<div class="fw homeSpreadAll">
    <div class="container">
        <div class="fw homeSpread">
            <div class="fw simpleTitle wow fadeInUp">
                <ul class="sul">
                    <li class="subTitle"><span class="subTitleX">RazeWin</span></li>
                    <li class="title">@Localizer["Live Price Stream"]</li>
                    <li class="desc">@Localizer["Invest with RazeWin"].</li>
                    <li class="rightButton"><a href="/market" class="simpleButton">@Localizer["VIEW ALL LIVE STREAM"]<i
                                class="flaticon-arrow-pointing-to-right"></i></a></li>
                </ul>
            </div>
            <div class="container">

                <div class="box">
                    <div class="tradingview-widget-container">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript"
                                src="/s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                            {
                                "symbol"
                            :
                                "BINANCE:ETHUSDT",
                                    "width"
                            :
                                350,
                                    "height"
                            :
                                220,
                                    "locale"
                            :
                                "tr",
                                    "dateRange"
                            :
                                "12M",
                                    "colorTheme"
                            :
                                "dark",
                                    "trendLineColor"
                            :
                                "rgba(0, 255, 0, 1)",
                                    "underLineColor"
                            :
                                "rgba(0, 255, 0, 0.3)",
                                    "underLineBottomColor"
                            :
                                "rgba(41, 98, 255, 0)",
                                    "isTransparent"
                            :
                                false,
                                    "autosize"
                            :
                                false,
                                    "largeChartUrl"
                            :
                                ""
                            }
                        </script>
                    </div>
                    <!-- TradingView Widget END -->

                </div>

                <div class="box">
                    <div class="tradingview-widget-container">
                        <div class="tradingview-widget-container__widget"></div>
                        <div class="tradingview-widget-copyright">
                            <script type="text/javascript"
                                    src="/s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js"
                                    async>
                                {
                                    "symbol"
                                :
                                    "FX:EURUSD",
                                        "width"
                                :
                                    350,
                                        "height"
                                :
                                    220,
                                        "locale"
                                :
                                    "tr",
                                        "dateRange"
                                :
                                    "12M",
                                        "colorTheme"
                                :
                                    "dark",
                                        "trendLineColor"
                                :
                                    "rgba(0, 255, 0, 1)",
                                        "underLineColor"
                                :
                                    "rgba(0, 255, 0, 0.3)",
                                        "underLineBottomColor"
                                :
                                    "rgba(41, 98, 255, 0)",
                                        "isTransparent"
                                :
                                    false,
                                        "autosize"
                                :
                                    false,
                                        "largeChartUrl"
                                :
                                    ""
                                }
                            </script>
                            <style>
                                .container {
                                    display: flex;
                                    flex-wrap: wrap;
                                }

                                .box {
                                    min-width: 200px;
                                    height: 200px;
                                    margin: 18px;
                                    background-color: #191919;
                                    margin-left: -8px;
                                }
                            </style>
                        </div>
                    </div>
                </div>
                <!-- TradingView Widget BEGIN -->
                <div class="box">
                    <div class="tradingview-widget-container">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript"
                                src="/s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                            {
                                "symbol"
                            :
                                "BINANCE:BTCUSDT",
                                    "width"
                            :
                                350,
                                    "height"
                            :
                                220,
                                    "locale"
                            :
                                "tr",
                                    "dateRange"
                            :
                                "12M",
                                    "colorTheme"
                            :
                                "dark",
                                    "trendLineColor"
                            :
                                "rgba(0, 255, 0, 1)",
                                    "underLineColor"
                            :
                                "rgba(0, 255, 0, 0.3)",
                                    "underLineBottomColor"
                            :
                                "rgba(41, 98, 255, 0)",
                                    "isTransparent"
                            :
                                false,
                                    "autosize"
                            :
                                false,
                                    "largeChartUrl"
                            :
                                ""
                            }
                        </script>
                    </div>
                </div>


                <!-- TradingView Widget END -->
            </div>

            <br><br><br>
            @await Component.InvokeAsync("CoinList", Model.Markets)
        </div>
    </div>
</div>

<!-- User Stats Section -->
<div class="fw userStatsSection wow fadeInUp">
    <div class="container">
        <div class="userStatsContainer">
            <div class="row">
                <div class="col-md-6">
                    <div class="userStatsContent">
                        <h2>@Localizer["Join Our Growing Community"]</h2>
                        <p>@Localizer["Be part of the RazeWin community and experience the future of cryptocurrency trading."]</p>
                        <div class="userStatsList">
                            <div class="userStatItem">
                                <div class="statIcon"><i class="flaticon-user"></i></div>
                                <div class="statInfo">
                                    <span class="statNumber">50,000+</span>
                                    <span class="statLabel">@Localizer["Registered Users"]</span>
                                </div>
                            </div>
                            <div class="userStatItem">
                                <div class="statIcon"><i class="flaticon-transfer"></i></div>
                                <div class="statInfo">
                                    <span class="statNumber">$250M+</span>
                                    <span class="statLabel">@Localizer["Trading Volume"]</span>
                                </div>
                            </div>
                            <div class="userStatItem">
                                <div class="statIcon"><i class="flaticon-globe"></i></div>
                                <div class="statInfo">
                                    <span class="statNumber">100+</span>
                                    <span class="statLabel">@Localizer["Countries"]</span>
                                </div>
                            </div>
                        </div>
                        <a href="/register" class="userStatsButton">@Localizer["Join Now"]</a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="userStatsImage">
                        <img src="/images/user-stats2.png" alt="RazeWin Community" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End User Stats Section -->

<div class="fw threeStepsAll cover wow fadeInUp"
     style="background-image:url(/public/front/fxyatirim/assets/images/three-steps.png)">
    <div class="container">
        <div class="fw threeSteps">
            <div class="fw threeStepsTitle">
                <ul class="sul">
                    <li class="title">@Localizer["Start Investing in 3 Steps"]</li>
                    <li class="desc">
                        3 Basit adım ile hesap sahibi olabilir ve yatırım işlemlerine başlayabilirsiniz!
                    </li>
                </ul>
            </div>
            <!--.title-->
            <div class="fw threeStepsBox">
                <div class="fw threeStepTab wow fadeIn">
                    <ul class="sul">
                        <li class="icon"><span class="iconX wow zoomIn delay-1s"><i
                                    class="blueX textX flaticon-user"></i><em class="orangeX">01</em></span></li>
                        <li class="title">@Localizer["Create Your Account"]</li>
                        <li class="desc">
                            Hemen 2 dakikanınızı ayırarak hesap oluşturun ve size özel olarak atanacak olan
                            müşteri temsilcisi sizi arasın!
                        </li>
                    </ul>
                </div>
                <!--.tab-->
                <div class="fw threeStepTab wow fadeIn">
                    <ul class="sul">
                        <li class="icon"><span class="iconX wow zoomIn delay-1s"><i
                                    class="blueX textX flaticon-transfer"></i><em class="orangeX">02</em></span></li>
                        <li class="title">@Localizer["Deposit to Your Account"]</li>
                        <li class="desc">70 Ödeme yöntemimizden birini kullanarak kolayca yatırım yapabilirsiniz!</li>
                    </ul>
                </div>
                <!--.tab-->
                <div class="fw threeStepTab wow fadeIn">
                    <ul class="sul">
                        <li class="icon"><span class="iconX wow zoomIn delay-1s"><i
                                    class="blueX textX flaticon-graph"></i><em class="orangeX">03</em></span></li>
                        <li class="title">@Localizer["Start Investing"]</li>
                        <li class="desc">
                            Her gün 35 dilde yayınlanan analizlerimizi, istatistklerimizi ve Size özel olarak atanan
                            yatırım uzmanınızın dediklerini inceleyerek yatırımlarınıza başlayın!
                        </li>
                    </ul>
                </div>
                <!--.tab-->
            </div>
            <!--.box-->
        </div>
        <!--.threeSteps-->
    </div>
    <!--.container-->
</div>
<style>
    /* Info Panel Styles */
    .infoPanelSection {
        background-color: #191919;
        padding: 50px 0;
        margin-bottom: 30px;
    }

    .infoPanel {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .infoPanelContent {
        padding: 30px;
        color: #fff;
    }

    .infoPanelContent h2 {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
    }

    .infoPanelContent p {
        font-size: 16px;
        margin-bottom: 25px;
        color: #ccc;
    }

    .infoPanelStats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
    }

    .statItem {
        text-align: center;
    }

    .statNumber {
        display: block;
        font-size: 28px;
        font-weight: 700;
        color: #f7931a; /* Bitcoin orange color */
        margin-bottom: 5px;
    }

    .statLabel {
        font-size: 14px;
        color: #ccc;
    }

    .infoPanelButton {
        display: inline-block;
        background-color: #f7931a;
        color: #fff;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .infoPanelButton:hover {
        background-color: #e67e00;
        transform: translateY(-2px);
    }

    .earnButton {
        background-color: #2a71d0;
        margin-left: 10px;
    }

    .earnButton:hover {
        background-color: #1e5bb0;
    }

    .infoPanelImage {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .infoPanelImage img {
        max-width: 100%;
        height: auto;
    }

    @@media (max-width: 768px) {
        .infoPanelStats {
            flex-direction: column;
            gap: 15px;
        }

        .infoPanelImage {
            margin-top: 20px;
        }
    }
    /* End Info Panel Styles */

    /* User Stats Styles */
    .userStatsSection {
        padding: 60px 0;
        background-color: #191919;
        margin-bottom: 30px;
    }

    .userStatsContainer {
        background-color: #252525;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .userStatsContent {
        padding-right: 30px;
    }

    .userStatsContent h2 {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
    }

    .userStatsContent p {
        font-size: 16px;
        margin-bottom: 30px;
        color: #ccc;
    }

    .userStatsList {
        margin-bottom: 30px;
    }

    .userStatItem {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .statIcon {
        width: 60px;
        height: 60px;
        background-color: #1e1e1e;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .statIcon i {
        font-size: 24px;
        color: #f7931a;
    }

    .statInfo {
        flex: 1;
    }

    .statNumber {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: #fff;
        margin-bottom: 5px;
    }

    .statLabel {
        font-size: 14px;
        color: #ccc;
    }

    .userStatsButton {
        display: inline-block;
        background-color: #f7931a;
        color: #fff;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .userStatsButton:hover {
        background-color: #e67e00;
        transform: translateY(-2px);
    }

    .userStatsImage {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .userStatsImage img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
    }

    @@media (max-width: 768px) {
        .userStatsContent {
            padding-right: 0;
            margin-bottom: 30px;
        }
    }
    /* End User Stats Styles */

    #testimonials {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        background-color: #191919;
    }

    .testimonial-heading {
        letter-spacing: 1px;
        margin: 30px 0px;
        padding: 10px 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .testimonial-heading h1 {
        font-size: 2.2rem;
        font-weight: 500;
        background-color: #202020;
        color: #ffffff;
        padding: 10px 20px;
    }

    .testimonial-heading span {
        font-size: 1.3rem;
        color: white;
        margin-bottom: 10px;
        letter-spacing: 2px;
        text-transform: uppercase;
    }

    .testimonial-box-container {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        width: 100%;
    }

    .testimonial-box {
        width: 500px;
        box-shadow: 2px 2px 30px #151515;
        background-color: #191919;
        padding: 20px;
        margin: 15px;
        cursor: pointer;
    }

    .profile-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;
    }

    .profile-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .profile {
        display: flex;
        align-items: center;
    }

    .name-user {
        display: flex;
        flex-direction: column;
    }

    .name-user strong {
        color: white;
        font-size: 15px;
        letter-spacing: 0.5px;
    }

    .name-user span {
        color: white;
        font-size: 12px;
    }

    .reviews {
        color: #f9d71c;
    }

    .box-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .client-comment p {
        font-size: 18px;
        color: white;
    }

    .testimonial-box:hover {
        transform: translateY(-10px);
        transition: all ease 0.3s;
    }

    @@media (max-width: 1060px) {
        .testimonial-box {
            width: 80%;
            padding: 40px;
            border-radius: 9px;
        }
    }

    @@media (max-width: 790px) {
        .testimonial-box {
            width: 100%;
        }

        .testimonial-heading h1 {
            font-size: 1.4rem;
        }
    }

    @@media (max-width: 340px) {
        .box-top {
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        .reviews {
            margin-top: 10px;
        }
    }

    ::selection {
        color: #191919;
        background-color: #252525;
    }

    /* App Download Buttons Styles */
    .first-slider-preview {
        padding-left: 200px;
        padding-right: 575px;
    }

    .first-slider-preview ul li {
        width: 310px !important;
    }

    .homeSliderLogo {
        text-align: center;
        margin-bottom: 15px;
    }

    .homeSliderLogo img {
        max-width: 180px;
        height: auto;
    }

    /* App button styles are now applied inline */

    .appStoreBtn {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        border-radius: 8px;
        color: #fff;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .appStoreBtn i {
        font-size: 24px;
        margin-right: 10px;
    }

    .appStoreBtn.googlePlay {
        background-color: #4285F4;
    }

    .appStoreBtn.appStore {
        background-color: #000;
    }

    .appStoreBtn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        color: #fff;
    }



    @@media (max-width: 1199px) and (min-width: 992px) {
        .first-slider-preview {
            padding-left: 120px !important;
        }
    }

    @@media (max-width: 991px)
    {
        .first-slider-preview {
            padding-left: 0px !important;
        }
    }

    @@media (max-width: 767px) {
        .appStoreBtn {
            font-size: 12px;
            margin-right: 5px;
        }

        .spacingTitle {
            font-size: 12px !important;
        }

        .first-slider-preview ul li {
            width: 238px !important;
        }
    }


    @@media (max-width: 993px) {
        .appButtons {
            flex-direction: row !important;
            justify-content: center;
            align-items: center;
            gap: 10px !important;
            display: flex !important;
        }

        .appStoreBtn {
            width: 160px;
            justify-content: center;
            padding: 8px 15px;
            display: inline-flex !important;
        }

        .homeSliderLogo img {
            max-width: 150px;
            margin: 0 auto;
            display: block;
        }

        .spacingTitle {
            font-size: 18px !important;
        }

        .discount span.discountX {
            font-size: 36px !important;
        }

        .discount-subtitle span {
            font-size: 22px !important;
        }

        .homeSliderTitle {
            font-size: 11px !important;
            text-align: center;
        }

        .spacingTitle {
            text-align: center;
        }

        /* Responsive styles for the second slider */
        .homePreview {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .homePreview ul {
            width: 100% !important;
        }
    }

    @@media (max-width: 480px) {
        .first-slider-preview {
            padding-right: 0 !important;
        }
        .spacingTitle {
            font-size: 16px !important;
        }

        .discount span.discountX {
            font-size: 28px !important;
            display: inline-block;
        }

        .discount-subtitle span {
            font-size: 18px !important;
            display: inline-block;
        }

        .homeSliderTitle {
            font-size: 10px !important;
        }

        .appStoreBtn {
            width: 140px;
            font-size: 14px;
            padding: 8px 10px;
            display: inline-flex !important;
        }

        .appStoreBtn i {
            font-size: 20px;
            margin-right: 5px;
        }

        /* Additional styles for the second slider on small screens */
        .desc {
            font-size: 12px !important;
            margin-bottom: 10px !important;
        }

        .link .simpleButton {
            padding: 8px 15px;
            font-size: 14px;
        }
    }

    @@media (max-width: 360px) {
        .appButtons {
            gap: 8px !important;
            flex-direction: row !important;
            display: flex !important;
        }

        .appStoreBtn {
            width: 130px;
            font-size: 13px;
            padding: 8px 8px;
            display: inline-flex !important;
        }

        .discount span.discountX {
            font-size: 24px !important;
        }

        .discount-subtitle span {
            font-size: 16px !important;
        }

        .spacingTitle {
            font-size: 14px !important;
        }

        .homeSliderTitle {
            font-size: 9px !important;
        }
    }</style>
<!--Testimonials------------------->
<section id="testimonials">
    <!--heading--->
    <div class="testimonial-heading">
        <span>@Localizer["Comments"]</span>
        <h1>@Localizer["What Our Customers Say"]</h1>
    </div>
    <div class="testimonial-box-container">
        <!--BOX-1-------------->
        <div class="testimonial-box">
            <!--top------------------------->
            <div class="box-top">
                <!--profile----->
                <div class="profile">
                    <!--img---->
                    <div class="profile-img">
                        <img src="/cdn.pixabay.com/photo/2016/03/12/14/09/young-man-1252041_960_720.jpg"/>
                    </div>
                    <!--name-and-username-->
                    <div class="name-user">
                        <strong>Ahmet Ersoy</strong>
                        <span>@@ahmet_erss1</span>
                    </div>
                </div>
                <!--reviews------>
                <div class="reviews">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="far fa-star"></i><!--Empty star-->
                </div>
            </div>
            <!--Comments---------------------------------------->
            <div class="client-comment">
                <p>"Razewin Token uygulaması ile sürüşler yaparak kazandığım tokenlerden kâr elde ettim.Çok teşekkürler
                    "Razewin" Sayenizde sürüşlerimden hâlâ para kazanıyorum.</p>
            </div>
        </div>
        <!--BOX-2-------------->
        <div class="testimonial-box">
            <!--top------------------------->
            <div class="box-top">
                <!--profile----->
                <div class="profile">
                    <!--img---->
                    <div class="profile-img">
                        <img src="/cdn.pixabay.com/photo/2016/03/12/14/09/man-1281562_1280.jpg"/>
                    </div>
                    <!--name-and-username-->
                    <div class="name-user">
                        <strong>Ali Yüreyir</strong>
                        <span>@@yureyir80ali</span>
                    </div>
                </div>
                <!--reviews------>
                <div class="reviews">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i><!--Empty star-->
                </div>
            </div>
            <!--Comments---------------------------------------->
            <div class="client-comment">
                <p>
                    "Seyahat eden herkesin aradığı ilk şey, "Bu projenin bir geleceği var mı?" soru bu
                    Büyük bir yatırım yaparak Razewin için en iyi cevabı vermiş olduk.
                    İşlerini gerçekten iyi yapıyorlar. İlk Seyahat madencilik kripto borsası ve token için iyi şanslar!"
                </p>
            </div>
        </div>
        <!--BOX-3-------------->
        <div class="testimonial-box">
            <!--top------------------------->
            <div class="box-top">
                <!--profile----->
                <div class="profile">
                    <!--img---->
                    <div class="profile-img">
                        <img src="/cdn.pixabay.com/photo/2016/03/12/14/09/istockphoto-1388349786-2048x2048.jpg"/>
                    </div>
                    <!--name-and-username-->
                    <div class="name-user">
                        <strong>Can Kabak</strong>
                        <span>@@cnkbak_01</span>
                    </div>
                </div>
                <!--reviews------>
                <div class="reviews">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="far fa-star"></i><!--Empty star-->
                </div>
            </div>
            <!--Comments---------------------------------------->
            <div class="client-comment">
                <p>
                    "Yeni gelişen dünyada yapay zeka tabanlı navigasyon ve seyahat kazancı sağlayan Token her geçen gün
                    önemini artırıyor.
                    Razewin, bu dünyanın merkezinde olmak için büyük adımlar atıyor.
                    Kripto para borsasına yeni bir devrim gelecek.
                    Ekip, ilk yapay zeka Navigasyon değişimi olarak büyük bir başarı elde etti."
                </p>
            </div>
        </div>
        <!--BOX-4-------------->
        <div class="testimonial-box">
            <!--top------------------------->
            <div class="box-top">
                <!--profile----->
                <div class="profile">
                    <!--img---->
                    <div class="profile-img">
                        <img src="/cdn.pixabay.com/photo/2016/03/12/14/09/man-2920911_1280.jpg"/>
                    </div>
                    <!--name-and-username-->
                    <div class="name-user">
                        <strong>Gürkan Dağıtan</strong>
                        <span>@@grkandgt</span>
                    </div>
                </div>
                <!--reviews------>
                <div class="reviews">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="far fa-star"></i>
                </div>
            </div>
            <!--Comments---------------------------------------->
            <div class="client-comment">
                <p>"Token Kazım dünyasında çok iyi bir proje olarak görüyoruz. Bu nedenle yatırımlarımızla
                    destekliyoruz. İnanılmaz bir başarı!"</p>
            </div>
        </div>
    </div>
</section>
<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>

<div class="fw homePlatformAll">
    <div class="container">
        <div class="fw homePlatform">
            <div class="row">
                <div class="col-md-12 col-lg-6 wow fadeInLeft">
                    <div class="fw homePlatformLeft">
                        <div class="fw simpleTitle">
                            <ul class="sul">

                                <li class="title">RZW TOKEN</li>
                                <li class="desc">@Localizer["EARN WHILE TRAVELING"].</li>
                            </ul>
                        </div>
                        <!--.title-->
                        <div class="fw mirstMetaMenus">
                            <ul role="tablist">
                            </ul>
                        </div>
                        <!--.menus-->
                        <div class="tab-content">
                            <div role="tabpanel" class="tab-pane fade in active" id="trader1">
                                <div class="fw simpleContent align-justify">
                                    <h3>RazeWin<a href="javascript:;"
                                                  class="h3More">@Localizer["Get More Information"]</a></h3>
                                    <p>
                                        Razewin uygulamasını indir, hiçbir yatırım yapmadan seyahat ederken RZW Token
                                        kazanmaya başla!
                                        Kazandığın tokenleri dilediğin gibi harca, özgürlüĞünü yaşa.
                                    </p>
                                </div>
                                <!--.content-->
                                <div class="fw metaMenus">
                                    <ul>


                                        <li class="green"><a target="_blank"
                                                             href="https://play.google.com/store/apps/details?id=com.razewin&amp;gl=TR&amp;pli=1"
                                                             class="simpleButton">@Localizer["Download for Android"]<i
                                                    class="flaticon-google-play"></i></a></li>
                                        <li class="black"><a target="_blank"
                                                             href="https://apps.apple.com/tr/app/razewin/id6569241832?l=tr"
                                                             class="simpleButton">@Localizer["Download for iOS"]<i
                                                    class="flaticon-apple"></i></a></li>
                                    </ul>
                                </div>
                                <!--.metaMenus-->
                            </div>
                            <!--.tabpanel-->
                        </div>
                        <!--.tabcontent-->
                    </div>
                    <!--.left-->
                </div>
                <!--.col-->
                <div class="col-md-12 col-lg-6 wow fadeInRight">
                    <div class="fw homePlatformRight wow zoomIn delay-1s">
                        <img src="meta.jpg"/>
                    </div>
                    <!--.right-->
                </div>
                <!--.col-->
            </div>
            <!--.row-->
        </div>
        <!--.homePlatform-->
    </div>
    <!--.container-->
</div>



<div class="fw threeStepsAll cover wow fadeInUp"
     style="  background-image:url(/public/front/fxyatirim/assets/images/three-steps.png); visibility: visible;animation-name: fadeInUp;padding: 50px 0;">
    <div class="container">
        <div class="fw threeSteps">
            <div class="fw threeStepsTitle">
                <ul class="sul">
                    <li class="title">@Localizer["Our Price Providers"]</li>
                </ul>
            </div>
            <div class="fw footerIcons" style="
                                                margin-bottom: 0px;
                                                ">
                <ul>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="pepperstone"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/pepperstone.png" style="padding: 29px;">
                        </span>
                    </li>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="swiss bank"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/swiss.png" style="padding: 30px;">
                        </span>
                    </li>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="citibank"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/citibank.png"
                                 style="padding: 26px; height: 80px;">
                        </span>
                    </li>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="deutsche bank"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/dbank.png" style="padding: 27px;">
                        </span>
                    </li>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="commerzbank"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/commerzbank.png" style="padding: 21px; ">
                        </span>
                    </li>
                    <li data-toggle="tooltip" data-placement="bottom" title="" class="wow zoomIn" data-wow-delay="0.1s"
                        data-original-title="goldman&sachs"
                        style="visibility: visible; animation-delay: 0.1s; animation-name: zoomIn; width: auto!important;">
                        <span class="title" style=" width: 195px;">
                            <img src="public/front/fxyatirim/assets/images/goldman.png"
                                 style="padding: 24px; height: 80px;">
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    @if (User?.Identity?.IsAuthenticated == true)
    {
        <script src="/site/components/coinlist/coinlist_auth.js"></script>
    }
    else
    {
        <script src="/site/components/coinlist/coinlist_unauth.js"></script>
    }
}
