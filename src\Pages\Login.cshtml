﻿@page
@model RazeWinComTr.Pages.LoginModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@inject IStringLocalizer<SharedResource> Localizer
@{
}

<div class="container">
    <div class="loginBox wow fadeInUp" style="margin-top: 80px; visibility: visible; animation-name: fadeInUp;">
        <div class="fw loginBoxDiv">
            <div class="fw loginBoxForm">
                <div class="fw loginPageTitle">
                    <ul class="sul">
                        <li class="img"><img src="public/image/GbErZcuBDF.png"></li>
                        <li class="title">@Localizer["Customer Login"]:</li>
                    </ul>
                </div>
                <!--.loginPageTitle-->
                <form method="post" asp-page-handler="OnPost">
                    <input type="hidden" asp-for="ReturnUrl" />
                    <div asp-validation-summary="All" class="text-danger" style="font-size: 18px; color: red;"></div>
                    <div class="fw loginPageForm">
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox BoxTop10">
                            </div>
                        </div>
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox BoxTop10">
                                <span class="icon"><i class="flaticon-arroba"></i></span>
                                <span class="title">@Localizer["Email Address"]</span>
                                <input asp-for="Input.Email" class="loginFormText" placeholder="Mail">
                                <span asp-validation-for="Input.Email" class="text-danger"></span>
                            </div>
                            <!--.tabBox-->
                        </div>
                        <!--.tab-->
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox BoxTop10">
                                <span class="icon"><i class="flaticon-key"></i></span>
                                <span class="title">@Localizer["Password"]:</span>
                                <input asp-for="Input.Password" type="password" class="loginFormText" placeholder="xxxxx">
                                <span asp-validation-for="Input.Password" class="text-danger"></span>
                            </div>
                            <!--.tabBox-->
                        </div>
                        <!--.tab-->
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormSubmit">
                                <button type="submit" class="simpleButton xLargeX">
                                    @Localizer["Login to My Account"] <i class="flaticon-arrow-pointing-to-right"></i>
                                </button>
                            </div>
                            <!--.submit-->
                        </div>
                        <!--.tab-->
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormSubmit">
                                <a asp-page="/Register" class="simpleButton orangeX smallX">
                                    @Localizer["Create Account"] <i class="flaticon-arrow-pointing-to-right"></i>
                                </a>
                            </div>
                            <!--.submit-->
                        </div>
                        <!--.tab-->
                    </div>
                    <!--.loginPageForm-->
                </form>
            </div>
            <!--.form-->
        </div>
        <!--.loginBoxDiv-->
    </div>
    <!--.loginBox-->
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}