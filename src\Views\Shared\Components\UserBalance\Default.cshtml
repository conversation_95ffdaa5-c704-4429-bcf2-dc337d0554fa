@model RazeWinComTr.ViewComponents.UserBalanceViewModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer

<div class="d-flex align-items-center">
    <!-- TRY Balance -->
    <span class="badge badge-success mr-2">
        @Model.BalanceText: @Model.FormattedTryBalance ₺
    </span>

    <!-- RZW Balance (if available) -->
    @if (Model.HasRzwBalance && Model.RzwBalance != null)
    {
        <span class="badge badge-info" style="background-color: #667eea;">
            RZW: @Model.FormattedRzwBalance
        </span>
    }
</div>
