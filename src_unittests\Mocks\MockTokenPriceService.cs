using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of ITokenPriceService for testing
/// </summary>
public class MockTokenPriceService : ITokenPriceService
{
    private readonly decimal _rzwPrice;
    private readonly decimal _rzwSellPrice;
    private readonly int _rzwTokenId;

    public MockTokenPriceService(decimal rzwPrice = 1.0m, int rzwTokenId = 1, decimal? rzwSellPrice = null)
    {
        _rzwPrice = rzwPrice;
        _rzwTokenId = rzwTokenId;
        _rzwSellPrice = rzwSellPrice ?? rzwPrice * 1.05m; // Default sell price is 5% higher than buy price
    }

    public Task<RzwTokenInfo> GetCoinInfoAsync(int coinId)
    {
        return Task.FromResult(new RzwTokenInfo { TokenId = coinId, BuyPrice = _rzwPrice, SellPrice = _rzwSellPrice });
    }

    /// <summary>
    /// Gets the current RZW token buy price in TRY
    /// </summary>
    public Task<decimal> GetCurrentRzwBuyPriceAsync()
    {
        return Task.FromResult(_rzwPrice);
    }

    /// <summary>
    /// Gets the RZW token ID
    /// </summary>
    public Task<int> GetRzwTokenIdAsync()
    {
        return Task.FromResult(_rzwTokenId);
    }

    /// <summary>
    /// Gets complete RZW token information including ID and prices
    /// </summary>
    public Task<RzwTokenInfo> GetRzwTokenInfoAsync()
    {
        return Task.FromResult(new RzwTokenInfo
        {
            TokenId = _rzwTokenId,
            BuyPrice = _rzwPrice,
            SellPrice = _rzwSellPrice
        });
    }
}
