using Microsoft.EntityFrameworkCore.Storage;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of IWalletService for testing
/// </summary>
public class MockWalletService : IWalletService
{
    private readonly Dictionary<(int UserId, int CoinId), Wallet> _wallets = new();
    private int _nextId = 1;
    private bool _shouldFail = false;
    private readonly TradeService? _tradeService;
    private readonly AppDbContext? _context;

    public MockWalletService(TradeService? tradeService = null, AppDbContext? context = null)
    {
        _tradeService = tradeService;
        _context = context;
    }

    // Track method calls for verification
    public int AddToWalletAsyncCallCount { get; private set; }
    public List<(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)> AddToWalletAsyncCalls { get; } = new();

    public void SetAddToWalletAsyncToFail()
    {
        _shouldFail = true;
    }

    public Task<Wallet?> GetByIdAsync(int id)
    {
        var wallet = _wallets.Values.FirstOrDefault(w => w.Id == id);
        return Task.FromResult(wallet);
    }

    public Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        var wallets = _wallets.Values.Where(w => w.UserId == userId).ToList();
        return Task.FromResult(wallets);
    }

    public Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        var wallets = _wallets.Values
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => w.Balance)
            .Take(topN)
            .ToList();
        return Task.FromResult(wallets);
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId)
    {
        _wallets.TryGetValue((userId, coinId), out var wallet);
        return Task.FromResult(wallet);
    }

    public Task<List<WalletViewModel>> GetListAsync()
    {
        var viewModels = _wallets.Values.Select(w => new WalletViewModel
        {
            Id = w.Id,
            UserId = w.UserId,
            CoinId = w.CoinId,
            Balance = w.Balance,
            CreatedDate = w.CreatedDate,
            ModifiedDate = w.ModifiedDate
        }).ToList();
        return Task.FromResult(viewModels);
    }

    public async Task<decimal> GetUserBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.Balance ?? 0;
    }

    public Task<Wallet> CreateAsync(Wallet wallet, IDbContextTransaction? existingTransaction = null)
    {
        wallet.Id = _nextId++;
        _wallets[(wallet.UserId, wallet.CoinId)] = wallet;
        return Task.FromResult(wallet);
    }

    public Task UpdateAsync(Wallet wallet)
    {
        if (_wallets.ContainsKey((wallet.UserId, wallet.CoinId)))
        {
            _wallets[(wallet.UserId, wallet.CoinId)] = wallet;
        }
        return Task.CompletedTask;
    }

    public Task DeleteAsync(int id)
    {
        var wallet = _wallets.Values.FirstOrDefault(w => w.Id == id);
        if (wallet != null)
        {
            _wallets.Remove((wallet.UserId, wallet.CoinId));
        }
        return Task.CompletedTask;
    }

    public async Task<Wallet> AddBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        AddToWalletAsyncCallCount++;
        AddToWalletAsyncCalls.Add((userId, rzwTokenInfo, amount));

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null)
        {
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                CreatedDate = DateTime.UtcNow
            };
            return await CreateAsync(wallet);
        }
        else
        {
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet);
            return wallet;
        }
    }

    public async Task<bool> DeductBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null || wallet.Balance < amount)
        {
            return false;
        }

        wallet.Balance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet);
        return true;
    }

    public async Task<Wallet> AddToWalletAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        if (_shouldFail)
        {
            throw new Exception("Simulated wallet service failure");
        }

        return await AddBalanceAsync(userId, rzwTokenInfo, amount);
    }

    // Helper method to verify calls
    public bool VerifyAddToWalletAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, int times = 1, decimal tolerance = 0.01m)
    {
        return AddToWalletAsyncCalls.Count(call =>
            call.userId == userId &&
            call.rzwTokenInfo.TokenId == rzwTokenInfo.TokenId &&
            Math.Abs(call.amount - amount) <= tolerance) == times;
    }

    // Helper method to debug wallet calls
    public List<(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)> GetAddToWalletAsyncCalls()
    {
        return AddToWalletAsyncCalls;
    }

    // NEW METHODS - Available Balance (Phase 2)
    public async Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        return await GetUserBalanceAsync(userId, coinId); // Available balance = Balance
    }

    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType)
    {
        decimal userTryBalance = 0;
        if (_context != null)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                userTryBalance = user.Balance;
            }
        }
        var wallet = await AddBalanceAsync(userId, rzwTokenInfo, amount);
        if (_tradeService != null)
        {
            var trade = new Trade
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Type = tradeType,
                CoinAmount = amount,
                CoinRate = rzwTokenInfo.BuyPrice,
                TryAmount = amount * rzwTokenInfo.BuyPrice,
                PreviousBalance = userTryBalance,
                NewBalance = userTryBalance,
                PreviousCoinBalance = wallet.Balance - amount,
                NewCoinBalance = wallet.Balance,
                PreviousWalletBalance = wallet.Balance - amount,
                NewWalletBalance = wallet.Balance
            };
            await _tradeService.CreateAsync(trade);
        }
        return wallet;
    }
    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction) => await AddAvailableBalanceAsync(userId, rzwTokenInfo, amount, tradeType);

    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await DeductBalanceAsync(userId, rzwTokenInfo, amount);
    }

    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction)
    {
        return await DeductBalanceAsync(userId, rzwTokenInfo, amount);
    }

    public async Task<Wallet> AddToAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await AddToWalletAsync(userId, rzwTokenInfo, amount);
    }

    // NEW METHODS - Locked Balance (Phase 2)
    public async Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.LockedBalance ?? 0;
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await LockBalanceAsync(userId, rzwTokenInfo, amount, null);
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction? transaction)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);
        if (wallet == null || wallet.Balance < amount)
        {
            return false;
        }

        // Move from available to locked
        wallet.Balance -= amount;
        wallet.LockedBalance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet);
        return true;
    }

    public async Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        return await UnlockBalanceAsync(userId, rzwTokenInfo, amount, null);
    }

    public async Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction? transaction)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);
        if (wallet == null || wallet.LockedBalance < amount)
        {
            return false;
        }

        // Move from locked to available
        wallet.LockedBalance -= amount;
        wallet.Balance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet);
        return true;
    }

    public async Task<Wallet> AddLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null)
        {
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = 0,
                LockedBalance = amount,
                CreatedDate = DateTime.UtcNow
            };
            return await CreateAsync(wallet);
        }
        else
        {
            wallet.LockedBalance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet);
            return wallet;
        }
    }

    public async Task<bool> DeductLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId);

        if (wallet == null || wallet.LockedBalance < amount)
        {
            return false;
        }

        wallet.LockedBalance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet);
        return true;
    }

    // NEW METHODS - Total Balance (Phase 2)
    public async Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.TotalBalance ?? 0;
    }

    // NEW METHODS - Balance Info (Phase 2)
    public async Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);

        return new WalletBalanceInfo
        {
            UserId = userId,
            CoinId = coinId,
            CoinCode = $"COIN{coinId}",
            CoinName = $"Test Coin {coinId}",
            IconUrl = "",
            AvailableBalance = wallet?.Balance ?? 0,
            LockedBalance = wallet?.LockedBalance ?? 0,
            LockedInSavings = coinId == 1 ? 0 : null, // Assume coinId 1 is RZW for testing
            SavingsLocks = coinId == 1 ? new List<SavingsLockInfo>() : null
        };
    }

    public async Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId)
    {
        var userWallets = _wallets.Values.Where(w => w.UserId == userId).ToList();
        var balanceInfos = new List<WalletBalanceInfo>();

        foreach (var wallet in userWallets)
        {
            var balanceInfo = await GetWalletBalanceInfoAsync(userId, wallet.CoinId);
            balanceInfos.Add(balanceInfo);
        }

        return balanceInfos;
    }
}
