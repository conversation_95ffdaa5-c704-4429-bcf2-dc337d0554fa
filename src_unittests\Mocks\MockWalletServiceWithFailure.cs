using Microsoft.EntityFrameworkCore.Storage;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of IWalletService that throws an exception on AddToWalletAsync
/// </summary>
public class MockWalletServiceWithFailure : IWalletService
{
    public Task<Wallet?> GetByIdAsync(int id)
    {
        return Task.FromResult<Wallet?>(null);
    }

    public Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        return Task.FromResult(new List<Wallet>());
    }

    public Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        return Task.FromResult(new List<Wallet>());
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId)
    {
        return Task.FromResult<Wallet?>(null);
    }

    public Task<List<WalletViewModel>> GetListAsync()
    {
        return Task.FromResult(new List<WalletViewModel>());
    }

    public Task<decimal> GetUserBalanceAsync(int userId, int coinId)
    {
        return Task.FromResult(0m);
    }

    public Task<Wallet> CreateAsync(Wallet wallet, IDbContextTransaction? existingTransaction)
    {
        return Task.FromResult(wallet);
    }

    public Task UpdateAsync(Wallet wallet)
    {
        return Task.CompletedTask;
    }

    public Task DeleteAsync(int id)
    {
        return Task.CompletedTask;
    }

    public Task<Wallet> AddBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> DeductBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddToWalletAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    // NEW METHODS - Available Balance (Phase 2) - All throw exceptions
    public Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType) => AddAvailableBalanceAsync(userId, rzwTokenInfo, amount);

    public Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction) => AddAvailableBalanceAsync(userId, rzwTokenInfo, amount, transaction);

    public Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddToAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    // NEW METHODS - Locked Balance (Phase 2) - All throw exceptions
    public Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, IDbContextTransaction? existingTransaction)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<bool> DeductLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        throw new Exception("Wallet service failure");
    }

    // NEW METHODS - Total Balance (Phase 2) - All throw exceptions
    public Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId)
    {
        throw new Exception("Wallet service failure");
    }

    // NEW METHODS - Balance Info (Phase 2) - All throw exceptions
    public Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId)
    {
        throw new Exception("Wallet service failure");
    }
}
