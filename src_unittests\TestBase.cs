using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Tests.Mocks;

namespace RazeWinComTr.Tests
{
    /// <summary>
    /// Base class for all test classes in the RazeWinComTr.Tests namespace.
    /// Provides common functionality for creating test database contexts, mock services, and loggers.
    /// </summary>
    public abstract class TestBase
    {
        /// <summary>
        /// Creates a new in-memory database context for testing.
        /// Each test should use a unique database name to ensure test isolation.
        /// </summary>
        /// <param name="databaseName">A unique name for the in-memory database</param>
        /// <returns>A new AppDbContext instance connected to an in-memory database</returns>
        protected AppDbContext CreateDbContext(string databaseName)
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName)
                .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;

            var context = new AppDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
            return context;
        }

        /// <summary>
        /// Creates a mock logger for testing.
        /// </summary>
        /// <typeparam name="T">The type that the logger is for</typeparam>
        /// <returns>A mock ILogger instance</returns>
        protected Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// Creates a mock WalletService for testing.
        /// </summary>
        /// <returns>A MockWalletService instance</returns>
        protected MockWalletService CreateMockWalletService()
        {
            return new MockWalletService();
        }
        /// <summary>
        /// Creates a mock WalletService for testing.
        /// </summary>
        /// <returns>A MockWalletService instance</returns>
        protected MockWalletService CreateMockWalletService(TradeService tradeService)
        {
            return new MockWalletService(tradeService);
        }


        /// <summary>
        /// Creates a mock TokenPriceService for testing.
        /// The mock is configured to return the specified RZW price for all RZW price-related calls.
        /// </summary>
        /// <param name="rzwPrice">The RZW price to return (default: 1.0)</param>
        /// <param name="rzwTokenId">The RZW token ID to return (default: 1)</param>
        /// <returns>A MockTokenPriceService instance</returns>
        protected MockTokenPriceService CreateMockTokenPriceService(decimal rzwPrice = 1.0m, int rzwTokenId = 1)
        {
            return new MockTokenPriceService(rzwPrice, rzwTokenId);
        }

        /// <summary>
        /// Creates a mock TradeService for testing.
        /// </summary>
        /// <returns>A MockTradeService instance</returns>
        protected MockTradeService CreateMockTradeService()
        {
            return new MockTradeService();
        }
        protected MockTradeService CreateMockTradeService(AppDbContext appDbContext)
        {
            return new MockTradeService(appDbContext);
        }
    }
}
