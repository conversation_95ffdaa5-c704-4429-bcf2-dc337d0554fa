using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.BackgroundServices;
using Xunit;

namespace RazeWinComTr.Tests.BackgroundServices;

public class RzwSavingsBackgroundServiceTests : IDisposable
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILogger<RzwSavingsBackgroundService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly RzwSavingsBackgroundService _service;

    public RzwSavingsBackgroundServiceTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLogger = new Mock<ILogger<RzwSavingsBackgroundService>>();

        // Create real configuration for testing
        var configData = new Dictionary<string, string>
        {
            {"RzwSavings:BackgroundService:ProcessingHour", "2"},
            {"RzwSavings:BackgroundService:ProcessingIntervalMinutes", "60"},
            {"RzwSavings:BackgroundService:BatchSize", "100"},
            {"RzwSavings:BackgroundService:EnableProcessing", "true"}
        };

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        _service = new RzwSavingsBackgroundService(
            _mockServiceProvider.Object,
            _mockLogger.Object,
            _configuration);
    }

    [Fact]
    public void Constructor_ShouldLoadConfigurationCorrectly()
    {
        // Arrange & Act - Constructor is called in setup

        // Assert
        Assert.NotNull(_service);
        // Configuration is loaded successfully if service is created without exception
    }

    [Fact]
    public async Task ExecuteAsync_WhenDisabled_ShouldNotProcess()
    {
        // Arrange
        var configData = new Dictionary<string, string>
        {
            {"RzwSavings:BackgroundService:ProcessingHour", "2"},
            {"RzwSavings:BackgroundService:ProcessingIntervalMinutes", "60"},
            {"RzwSavings:BackgroundService:BatchSize", "100"},
            {"RzwSavings:BackgroundService:EnableProcessing", "false"}
        };

        var disabledConfig = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        var service = new RzwSavingsBackgroundService(
            _mockServiceProvider.Object,
            _mockLogger.Object,
            disabledConfig);

        using var cts = new CancellationTokenSource();
        cts.CancelAfter(TimeSpan.FromMilliseconds(100));

        // Act
        await service.StartAsync(cts.Token);
        await Task.Delay(50);
        await service.StopAsync(cts.Token);

        // Assert
        // Service should not process when disabled - we can't easily verify CreateScope()
        // because it's an extension method, but the test passes if no exception is thrown
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
