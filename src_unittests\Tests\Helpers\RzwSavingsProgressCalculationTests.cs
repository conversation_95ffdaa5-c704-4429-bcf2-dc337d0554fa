using RazeWinComTr.Areas.Admin.Helpers;
using Xunit;

namespace RazeWinComTr.Tests.Helpers
{
    public class RzwSavingsProgressCalculationTests
    {
        [Fact]
        public void CalculatePreciseProgressPercentage_OneDayPlan_ShouldShowAccurateProgress()
        {
            // Arrange - 1 günlük plan, 12 saat geçmiş (50% tamamlanmış olmalı)
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1); // 1 günlük plan
            var currentDate = startDate.AddHours(12); // 12 saat geçmiş

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(50.0m, progressPercentage, 1); // 1 decimal tolerance
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_OneDayPlan_SixHours_ShouldShow25Percent()
        {
            // Arrange - 1 günlük plan, 6 saat geçmiş (25% tamamlanmış olmalı)
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = startDate.AddHours(6); // 6 saat geçmiş

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(25.0m, progressPercentage, 1);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_OneDayPlan_OneMinute_ShouldShowSmallProgress()
        {
            // Arrange - 1 günlük plan, 1 dakika geçmiş
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = startDate.AddMinutes(1); // 1 dakika geçmiş

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            // 1 dakika = 1/1440 gün = ~0.0694%
            var expectedPercentage = (1.0m / 1440.0m) * 100; // 1 dakika / 1440 dakika (1 gün)
            Assert.Equal(expectedPercentage, progressPercentage, 3); // 3 decimal tolerance
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_CompareWithDayBasedCalculation()
        {
            // Arrange - 1 günlük plan, 12 saat 30 dakika geçmiş
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = startDate.AddHours(12).AddMinutes(30); // 12.5 saat geçmiş

            // Act - Saniye bazlı hesaplama
            var preciseProgress = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Act - Eski gün bazlı hesaplama
            var totalDays = (maturityDate - startDate).Days;
            var elapsedDays = (currentDate - startDate).Days;
            var dayBasedProgress = totalDays > 0 ? (elapsedDays * 100.0m / totalDays) : 0;

            // Assert
            // Saniye bazlı hesaplama: 12.5 saat / 24 saat = 52.08%
            Assert.Equal(52.08m, preciseProgress, 1);
            
            // Gün bazlı hesaplama: 0 gün / 1 gün = 0% (çok hassasiyetsiz!)
            Assert.Equal(0m, dayBasedProgress);
            
            // Saniye bazlı hesaplama çok daha hassas
            Assert.True(preciseProgress > dayBasedProgress);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_SevenDayPlan_ShouldBeAccurate()
        {
            // Arrange - 7 günlük plan, 3.5 gün geçmiş (50% tamamlanmış olmalı)
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(7);
            var currentDate = startDate.AddDays(3).AddHours(12); // 3.5 gün geçmiş

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(50.0m, progressPercentage, 1);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_BeforeStart_ShouldReturnZero()
        {
            // Arrange - Henüz başlamamış plan
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = startDate.AddHours(-1); // Başlangıçtan 1 saat önce

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(0m, progressPercentage);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_AfterMaturity_ShouldReturn100()
        {
            // Arrange - Vadesi geçmiş plan
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = maturityDate.AddHours(1); // Vadeden 1 saat sonra

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(100m, progressPercentage);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_ExactMaturity_ShouldReturn100()
        {
            // Arrange - Tam vade tarihi
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = startDate.AddDays(1);
            var currentDate = maturityDate; // Tam vade tarihi

            // Act
            var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(
                startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(100m, progressPercentage);
        }

        [Fact]
        public void CalculateElapsedSeconds_ShouldReturnCorrectValue()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = startDate.AddHours(1); // 1 saat = 3600 saniye

            // Act
            var elapsedSeconds = RzwSavingsCalculationHelper.CalculateElapsedSeconds(startDate, currentDate);

            // Assert
            Assert.Equal(3600, elapsedSeconds);
        }

        [Fact]
        public void CalculateRemainingSeconds_ShouldReturnCorrectValue()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 11, 0, 0, DateTimeKind.Utc); // 1 saat kaldı

            // Act
            var remainingSeconds = RzwSavingsCalculationHelper.CalculateRemainingSeconds(maturityDate, currentDate);

            // Assert
            Assert.Equal(3600, remainingSeconds); // 1 saat = 3600 saniye
        }

        [Fact]
        public void TotalDaysCalculation_OneDayPlan_ShouldReturn1()
        {
            // Arrange - 1 günlük vadeli hesap senaryosu
            var startDate = new DateTime(2024, 1, 1, 10, 30, 0, DateTimeKind.Utc); // 10:30
            var maturityDate = startDate.AddDays(1); // Ertesi gün 10:30

            // Act - TotalDays hesaplama (Details.cshtml.cs'deki gibi)
            var totalDays = (maturityDate - startDate).Days;

            // Assert
            Assert.Equal(1, totalDays); // 1 gün olmalı, 2 değil!
        }

        [Fact]
        public void TotalDaysCalculation_OneDayPlan_DifferentTimes_ShouldReturn1()
        {
            // Arrange - Farklı saatlerde başlayan 1 günlük vadeli hesaplar
            var scenarios = new[]
            {
                new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),   // Gece yarısı
                new DateTime(2024, 1, 1, 6, 0, 0, DateTimeKind.Utc),   // Sabah 6
                new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),  // Öğlen
                new DateTime(2024, 1, 1, 18, 0, 0, DateTimeKind.Utc),  // Akşam 6
                new DateTime(2024, 1, 1, 23, 59, 59, DateTimeKind.Utc) // Gece yarısından 1 saniye önce
            };

            foreach (var startDate in scenarios)
            {
                // Act
                var maturityDate = startDate.AddDays(1);
                var totalDays = (maturityDate - startDate).Days;

                // Assert
                Assert.Equal(1, totalDays); // Her durumda 1 gün olmalı
            }
        }
    }
}
