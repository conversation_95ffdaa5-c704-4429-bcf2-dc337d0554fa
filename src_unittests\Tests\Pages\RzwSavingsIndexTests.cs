using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;
using System.Security.Claims;
using Xunit;

namespace RazeWinComTr.Tests.Pages
{
    public class RzwSavingsIndexTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly RzwSavingsService _rzwSavingsService;
        private readonly RzwSavingsInterestService _rzwInterestService;
        private readonly RzwBalanceManagementService _rzwBalanceService;
        private readonly RzwSavingsPlanService _rzwPlanService;
        private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
        private readonly Mock<ILogger<IndexModel>> _mockLogger;

        public RzwSavingsIndexTests()
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new AppDbContext(options);

            // Setup mocks
            _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            _mockLogger = new Mock<ILogger<IndexModel>>();

            // Setup localizer to return the key as value
            _mockLocalizer.Setup(l => l[It.IsAny<string>()])
                .Returns((string key) => new LocalizedString(key, key));

            // Create services with mocked dependencies similar to existing tests
            var mockTokenPriceService = new Mock<ITokenPriceService>();
            var mockWalletService = new Mock<IWalletService>();
            var mockTradeService = new Mock<TradeService>(null!, _context);
            var mockBalanceLogger = new Mock<ILogger<RzwBalanceManagementService>>();
            var mockInterestLogger = new Mock<ILogger<RzwSavingsInterestService>>();
            var mockSavingsLogger = new Mock<ILogger<RzwSavingsService>>();
            var mockPlanLogger = new Mock<ILogger<RzwSavingsPlanService>>();

            // Setup token price service to return RZW token ID
            mockTokenPriceService.Setup(x => x.GetRzwTokenIdAsync()).ReturnsAsync(1);

            _rzwBalanceService = new RzwBalanceManagementService(_context, mockWalletService.Object, mockTokenPriceService.Object, mockTradeService.Object);
            _rzwPlanService = new RzwSavingsPlanService(_context, _mockLocalizer.Object, mockPlanLogger.Object);
            _rzwInterestService = new RzwSavingsInterestService(_context, _rzwBalanceService, _rzwPlanService, mockTradeService.Object, mockTokenPriceService.Object, _mockLocalizer.Object, mockInterestLogger.Object);
            _rzwSavingsService = new RzwSavingsService(_context, _rzwBalanceService, _rzwPlanService, _rzwInterestService, _mockLocalizer.Object, mockSavingsLogger.Object);

            SetupTestData();
        }

        [Fact]
        public async Task GetUserAllSavingsAsync_WithAccountsHavingEarnedInterest_ShouldReturnCorrectSum()
        {
            // Arrange
            var userId = 1;

            // Create test accounts with earned interest
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Test Plan",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 30,
                InterestRate = 0.001m,
                IsActive = true,
                MinRzwAmount = 100m,
                MaxRzwAmount = 10000m,
                CreatedDate = DateTime.UtcNow
            };
            _context.RzwSavingsPlans.Add(plan);

            var account1 = new RzwSavingsAccount
            {
                UserId = userId,
                PlanId = 1,
                RzwAmount = 1000m,
                TotalEarnedRzw = 50m, // Earned 50 RZW
                Status = RzwSavingsStatus.Matured,
                StartDate = DateTime.UtcNow.AddDays(-30),
                MaturityDate = DateTime.UtcNow.AddDays(-1),
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 30,
                CreatedDate = DateTime.UtcNow.AddDays(-30)
            };

            var account2 = new RzwSavingsAccount
            {
                UserId = userId,
                PlanId = 1,
                RzwAmount = 2000m,
                TotalEarnedRzw = 75m, // Earned 75 RZW
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-15),
                MaturityDate = DateTime.UtcNow.AddDays(15),
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 30,
                CreatedDate = DateTime.UtcNow.AddDays(-15)
            };

            _context.RzwSavingsAccounts.AddRange(account1, account2);
            await _context.SaveChangesAsync();

            // Act
            var allAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId);
            var totalEarned = allAccounts.Sum(a => a.TotalEarnedRzw);

            // Assert
            Assert.Equal(2, allAccounts.Count);
            Assert.Equal(50m, allAccounts.First(a => a.RzwAmount == 1000m).TotalEarnedRzw);
            Assert.Equal(75m, allAccounts.First(a => a.RzwAmount == 2000m).TotalEarnedRzw);

            // This is the core of our fix - sum of TotalEarnedRzw should be 125
            Assert.Equal(125m, totalEarned);

            // Verify that it's not 0 (the bug we're fixing)
            Assert.True(totalEarned > 0);
        }

        private IndexModel CreatePageModel()
        {
            var pageModel = new IndexModel(
                _rzwSavingsService,
                _rzwBalanceService,
                _rzwPlanService,
                _rzwInterestService,
                _mockLocalizer.Object);

            // Setup PageContext and User claims
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }));

            pageModel.PageContext = new PageContext
            {
                HttpContext = httpContext
            };

            return pageModel;
        }

        private void SetupTestData()
        {
            // Add test user
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                Name = "Test User",
                Surname = "User",
                IdentityNumber = "12345678901",
                PhoneNumber = "1234567890",
                BirthDate = DateTime.Now.AddYears(-25),
                IsActive = 1,
                CrDate = DateTime.UtcNow
            };
            _context.Users.Add(user);

            // Add RZW market
            var rzwMarket = new Market
            {
                Id = 1,
                Coin = "RZW",
                Name = "RazeWin Token",
                ShortName = "RZW",
                PairCode = "RZW-TRY",
                IsActive = 1,
                CrDate = DateTime.UtcNow
            };
            _context.Markets.Add(rzwMarket);

            // Add RZW wallet
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 10000m,
                LockedBalance = 0m
            };
            _context.Wallets.Add(wallet);

            _context.SaveChanges();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
