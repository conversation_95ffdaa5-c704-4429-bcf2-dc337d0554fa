using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;
using RazeWinComTr.Tests.Mocks;
using Xunit;

namespace RazeWinComTr.Tests.Services;

/// <summary>
/// Unit tests for RzwBalanceManagementService
/// Tests RZW-specific balance operations including savings locks
/// </summary>
public class RzwBalanceManagementServiceTests : TestBase
{
    /// <summary>
    /// Tests that GetRzwTokenIdAsync returns the correct RZW token ID from TokenPriceService
    /// </summary>
    [Fact]
    public async Task GetRzwTokenIdAsync_ReturnsCorrectTokenId()
    {
        // Arrange
        var dbContext = CreateDbContext("GetRzwTokenIdAsync_ReturnsCorrectTokenId");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 5);
        var mockTradeService = CreateMockTradeService(dbContext);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var rzwTokenInfo = await rzwBalanceService.GetRzwTokenInfoAsync();

        // Assert
        Assert.Equal(5, rzwTokenInfo.TokenId);
    }

    /// <summary>
    /// Tests that HasSufficientAvailableRzwAsync correctly checks available RZW balance
    /// </summary>
    [Fact]
    public async Task HasSufficientAvailableRzwAsync_WithSufficientBalance_ReturnsTrue()
    {
        // Arrange
        var dbContext = CreateDbContext("HasSufficientAvailableRzwAsync_WithSufficientBalance_ReturnsTrue");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet with sufficient balance
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var hasSufficient = await rzwBalanceService.HasSufficientAvailableRzwAsync(user.UserId, 50m);

        // Assert
        Assert.True(hasSufficient);
    }

    /// <summary>
    /// Tests that HasSufficientAvailableRzwAsync returns false when insufficient balance
    /// </summary>
    [Fact]
    public async Task HasSufficientAvailableRzwAsync_WithInsufficientBalance_ReturnsFalse()
    {
        // Arrange
        var dbContext = CreateDbContext("HasSufficientAvailableRzwAsync_WithInsufficientBalance_ReturnsFalse");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet with insufficient balance
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 30m, 50m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var hasSufficient = await rzwBalanceService.HasSufficientAvailableRzwAsync(user.UserId, 50m);

        // Assert
        Assert.False(hasSufficient);
    }

    /// <summary>
    /// Tests that GetRzwBalanceInfoAsync returns comprehensive RZW balance information
    /// </summary>
    [Fact]
    public async Task GetRzwBalanceInfoAsync_ReturnsCompleteRzwBalanceInfo()
    {
        // Arrange
        var dbContext = CreateDbContext("GetRzwBalanceInfoAsync_ReturnsCompleteRzwBalanceInfo");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var rzwBalanceInfo = await rzwBalanceService.GetRzwBalanceInfoAsync(user.UserId);

        // Assert
        Assert.Equal(user.UserId, rzwBalanceInfo.UserId);
        Assert.Equal(100m, rzwBalanceInfo.AvailableRzw);
        Assert.Equal(50m, rzwBalanceInfo.LockedRzw);
        Assert.Equal(150m, rzwBalanceInfo.TotalRzw);
        Assert.True(rzwBalanceInfo.HasRzwBalance);
        Assert.True(rzwBalanceInfo.HasAvailableRzw);
        Assert.True(rzwBalanceInfo.HasLockedRzw);
    }

    /// <summary>
    /// Tests that LockRzwForSavingsAsync successfully locks RZW for savings
    /// </summary>
    [Fact]
    public async Task LockRzwForSavingsAsync_WithSufficientBalance_LocksRzwSuccessfully()
    {
        // Arrange
        var dbContext = CreateDbContext("LockRzwForSavingsAsync_WithSufficientBalance_LocksRzwSuccessfully");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet with sufficient balance
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 20m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var result = await rzwBalanceService.LockRzwForSavingsAsync(user.UserId, 30m, "Test savings lock");

        // Assert
        Assert.True(result);

        // Verify balance was locked
        var updatedWallet = await mockWalletService.GetByUserIdAndCoinIdAsync(user.UserId, market.Id);
        Assert.Equal(70m, updatedWallet!.Balance); // 100 - 30
        Assert.Equal(50m, updatedWallet.LockedBalance); // 20 + 30

        // Verify trade record was created
        var trades = await dbContext.Trades.Where(t => t.UserId == user.UserId).ToListAsync();
        Assert.Single(trades);
        Assert.Equal(TradeType.RzwSavingsDeposit, trades[0].Type);
        Assert.Equal(30m, trades[0].CoinAmount);
    }

    /// <summary>
    /// Tests that LockRzwForSavingsAsync fails when insufficient balance
    /// </summary>
    [Fact]
    public async Task LockRzwForSavingsAsync_WithInsufficientBalance_ReturnsFalse()
    {
        // Arrange
        var dbContext = CreateDbContext("LockRzwForSavingsAsync_WithInsufficientBalance_ReturnsFalse");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet with insufficient balance
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 20m, 10m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var result = await rzwBalanceService.LockRzwForSavingsAsync(user.UserId, 50m, "Test savings lock");

        // Assert
        Assert.False(result);

        // Verify balance unchanged
        var updatedWallet = await mockWalletService.GetByUserIdAndCoinIdAsync(user.UserId, market.Id);
        Assert.Equal(20m, updatedWallet!.Balance);
        Assert.Equal(10m, updatedWallet.LockedBalance);
    }

    /// <summary>
    /// Tests that UnlockRzwFromSavingsAsync successfully unlocks RZW from savings
    /// </summary>
    [Fact]
    public async Task UnlockRzwFromSavingsAsync_WithSufficientLockedBalance_UnlocksRzwSuccessfully()
    {
        // Arrange
        var dbContext = CreateDbContext("UnlockRzwFromSavingsAsync_WithSufficientLockedBalance_UnlocksRzwSuccessfully");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet with locked balance
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 50m, 80m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var result = await rzwBalanceService.UnlockRzwFromSavingsAsync(user.UserId, 30m, "Test savings unlock");

        // Assert
        Assert.True(result);

        // Verify balance was unlocked
        var updatedWallet = await mockWalletService.GetByUserIdAndCoinIdAsync(user.UserId, market.Id);
        Assert.Equal(80m, updatedWallet!.Balance); // 50 + 30
        Assert.Equal(50m, updatedWallet.LockedBalance); // 80 - 30

        // Verify trade record was created
        var trades = await dbContext.Trades.Where(t => t.UserId == user.UserId).ToListAsync();
        Assert.Single(trades);
        Assert.Equal(TradeType.RzwSavingsWithdrawal, trades[0].Type);
        Assert.Equal(30m, trades[0].CoinAmount);
    }

    /// <summary>
    /// Tests that AddRzwInterestAsync successfully adds interest to available balance
    /// </summary>
    [Fact]
    public async Task AddRzwInterestAsync_AddsInterestToAvailableBalance()
    {
        // Arrange
        var dbContext = CreateDbContext("AddRzwInterestAsync_AddsInterestToAvailableBalance");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var result = await rzwBalanceService.AddRzwInterestAsync(user.UserId, 10m, "Test interest payment");

        // Assert
        Assert.NotNull(result);

        // Verify interest was added to available balance
        var updatedWallet = await mockWalletService.GetByUserIdAndCoinIdAsync(user.UserId, market.Id);
        Assert.Equal(110m, updatedWallet!.Balance); // 100 + 10
        Assert.Equal(50m, updatedWallet.LockedBalance); // Unchanged

        // Verify trade record was created
        var trades = await dbContext.Trades.Where(t => t.UserId == user.UserId).ToListAsync();
        Assert.Single(trades);
        Assert.Equal(TradeType.RzwSavingsInterest, trades[0].Type);
        Assert.Equal(10m, trades[0].CoinAmount);
    }

    /// <summary>
    /// Tests that DeductAvailableRzwAsync successfully deducts from available balance
    /// </summary>
    [Fact]
    public async Task DeductAvailableRzwAsync_WithSufficientBalance_DeductsSuccessfully()
    {
        // Arrange
        var dbContext = CreateDbContext("DeductAvailableRzwAsync_WithSufficientBalance_DeductsSuccessfully");
        var mockWalletService = CreateMockWalletService();
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m, rzwTokenId: 1);
        var mockTradeService = CreateMockTradeService(dbContext);

        // Setup wallet
        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        await mockWalletService.CreateAsync(wallet);

        var rzwBalanceService = new RzwBalanceManagementService(
            dbContext, mockWalletService, mockTokenPriceService, mockTradeService);

        // Act
        var result = await rzwBalanceService.DeductAvailableRzwAsync(user.UserId, 20m);

        // Assert
        Assert.True(result);

        // Verify amount was deducted from available balance
        var updatedWallet = await mockWalletService.GetByUserIdAndCoinIdAsync(user.UserId, market.Id);
        Assert.Equal(80m, updatedWallet!.Balance); // 100 - 20
        Assert.Equal(50m, updatedWallet.LockedBalance); // Unchanged
    }
}
