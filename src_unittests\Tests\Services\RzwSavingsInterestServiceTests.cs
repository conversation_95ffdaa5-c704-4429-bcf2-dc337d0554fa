using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using Xunit;

namespace RazeWinComTr.Tests.Services;

public class RzwSavingsInterestServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<RzwBalanceManagementService> _mockBalanceService;
    private readonly Mock<RzwSavingsPlanService> _mockPlanService;
    private readonly Mock<TradeService> _mockTradeService;
    private readonly Mock<ITokenPriceService> _mockTokenPriceService;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<RzwSavingsInterestService>> _mockLogger;
    private readonly RzwSavingsInterestService _service;

    public RzwSavingsInterestServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _context = new AppDbContext(options);

        _mockBalanceService = new Mock<RzwBalanceManagementService>(
            _context, null!, null!, null!);
        _mockPlanService = new Mock<RzwSavingsPlanService>(
            _context, null!, null!);
        _mockTradeService = new Mock<TradeService>(null!, _context);
        _mockTokenPriceService = new Mock<ITokenPriceService>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<RzwSavingsInterestService>>();

        _service = new RzwSavingsInterestService(
            _context,
            _mockBalanceService.Object,
            _mockPlanService.Object,
            _mockTradeService.Object,
            _mockTokenPriceService.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        _mockTokenPriceService.Setup(x => x.GetRzwTokenIdAsync())
            .ReturnsAsync(1);
    }

    [Fact]
    public async Task CalculateDailyInterestAsync_DailyPlan_ShouldCalculateCorrectly()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Daily,
            InterestRate = 0.001m, // 0.1% daily
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            TotalEarnedRzw = 0m,
            Status = RzwSavingsStatus.Active
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CalculateDailyInterestAsync(account);

        // Assert
        var expectedInterest = 1000m * 0.001m; // 1 RZW
        Assert.Equal(expectedInterest, result);
    }

    [Fact]
    public async Task CalculateDailyInterestAsync_MonthlyPlan_ShouldCalculateCorrectly()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Monthly,
            InterestRate = 0.03m, // 3% monthly
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            TotalEarnedRzw = 0m,
            Status = RzwSavingsStatus.Active
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CalculateDailyInterestAsync(account);

        // Assert
        var expectedDailyRate = 0.03m / 30m; // Monthly rate divided by 30
        var expectedInterest = 1000m * expectedDailyRate;
        Assert.Equal(Math.Round(expectedInterest, 8, MidpointRounding.AwayFromZero), result);
    }

    [Fact]
    public async Task CalculateDailyInterestAsync_YearlyPlan_ShouldCalculateCorrectly()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Yearly,
            InterestRate = 0.12m, // 12% yearly
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            TotalEarnedRzw = 0m,
            Status = RzwSavingsStatus.Active
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CalculateDailyInterestAsync(account);

        // Assert
        var expectedDailyRate = 0.12m / 365m; // Yearly rate divided by 365
        var expectedInterest = 1000m * expectedDailyRate;
        Assert.Equal(Math.Round(expectedInterest, 8, MidpointRounding.AwayFromZero), result);
    }

    [Fact]
    public async Task CalculateDailyInterestAsync_WithAccumulatedInterest_ShouldUseCompoundPrincipal()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Daily,
            InterestRate = 0.001m, // 0.1% daily
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            TotalEarnedRzw = 50m, // Already earned 50 RZW
            Status = RzwSavingsStatus.Active
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CalculateDailyInterestAsync(account);

        // Assert
        var currentPrincipal = 1000m + 50m; // 1050 RZW
        var expectedInterest = currentPrincipal * 0.001m; // 1.05 RZW
        Assert.Equal(expectedInterest, result);
    }

    [Fact]
    public async Task CalculateDailyInterestAsync_InactiveAccount_ShouldReturnZero()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Daily,
            InterestRate = 0.001m,
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            TotalEarnedRzw = 0m,
            Status = RzwSavingsStatus.Matured // Not active
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CalculateDailyInterestAsync(account);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void CalculateCompoundInterest_ValidInputs_ShouldCalculateCorrectly()
    {
        // Arrange
        var principal = 1000m;
        var dailyRate = 0.001m; // 0.1% daily
        var days = 30;

        // Act
        var result = _service.CalculateCompoundInterest(principal, dailyRate, days);

        // Assert
        // Formula: P * (1 + r)^n - P
        var expectedFinalAmount = principal * (decimal)Math.Pow((double)(1 + dailyRate), days);
        var expectedInterest = expectedFinalAmount - principal;
        // Round to 8 decimal places to match the helper method's precision
        var expectedInterestRounded = Math.Round(expectedInterest, 8, MidpointRounding.AwayFromZero);
        Assert.Equal(expectedInterestRounded, result);
    }

    [Fact]
    public void CalculateCompoundInterest_ZeroDays_ShouldReturnZero()
    {
        // Act
        var result = _service.CalculateCompoundInterest(1000m, 0.001m, 0);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void CalculateCompoundInterest_ZeroRate_ShouldReturnZero()
    {
        // Act
        var result = _service.CalculateCompoundInterest(1000m, 0m, 30);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public async Task CalculateEarlyWithdrawalInterestAsync_WithEligiblePlan_ShouldCalculateCorrectly()
    {
        // Arrange
        var dailyPlan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            IsActive = true
        };

        var monthlyPlan = new RzwSavingsPlan
        {
            Id = 2,
            TermType = RzwSavingsTermType.Monthly,
            TermDuration = 30,
            InterestRate = 0.03m,
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 2, // Monthly plan
            RzwAmount = 1000m,
            Status = RzwSavingsStatus.Active,
            StartDate = DateTime.UtcNow.AddDays(-15) // Held for 15 days
        };

        _context.RzwSavingsPlans.AddRange(new[] { dailyPlan, monthlyPlan });
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Create real plan service for this test
        var mockPlanLogger = new Mock<ILogger<RzwSavingsPlanService>>();
        var realPlanService = new RzwSavingsPlanService(_context, _mockLocalizer.Object, mockPlanLogger.Object);
        var realService = new RzwSavingsInterestService(
            _context,
            _mockBalanceService.Object,
            realPlanService,
            _mockTradeService.Object,
            _mockTokenPriceService.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        // Act
        var result = await realService.CalculateEarlyWithdrawalInterestAsync(account);

        // Assert
        // Should use daily plan rate (1 day duration <= 15 days held)
        // Daily rate for daily plan = 0.001
        var expectedInterest = realService.CalculateCompoundInterest(1000m, 0.001m, 15);
        Assert.Equal(Math.Round(expectedInterest, 8, MidpointRounding.AwayFromZero), result);
    }

    [Fact]
    public async Task CalculateEarlyWithdrawalInterestAsync_NoEligiblePlan_ShouldReturnZero()
    {
        // Arrange
        var monthlyPlan = new RzwSavingsPlan
        {
            Id = 1,
            TermType = RzwSavingsTermType.Monthly,
            TermDuration = 30,
            InterestRate = 0.03m,
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = 1,
            PlanId = 1,
            RzwAmount = 1000m,
            Status = RzwSavingsStatus.Active,
            StartDate = DateTime.UtcNow.AddDays(-15) // Held for 15 days, but minimum plan is 30 days
        };

        _context.RzwSavingsPlans.Add(monthlyPlan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Create real plan service for this test
        var mockPlanLogger2 = new Mock<ILogger<RzwSavingsPlanService>>();
        var realPlanService = new RzwSavingsPlanService(_context, _mockLocalizer.Object, mockPlanLogger2.Object);
        var realService = new RzwSavingsInterestService(
            _context,
            _mockBalanceService.Object,
            realPlanService,
            _mockTradeService.Object,
            _mockTokenPriceService.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        // Act
        var result = await realService.CalculateEarlyWithdrawalInterestAsync(account);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public async Task GetUserTotalEarnedInterestAsync_ShouldReturnCorrectTotal()
    {
        // Arrange
        var userId = 1;
        var account1 = new RzwSavingsAccount { Id = 1, UserId = userId };
        var account2 = new RzwSavingsAccount { Id = 2, UserId = userId };
        var account3 = new RzwSavingsAccount { Id = 3, UserId = 2 }; // Different user

        var payments = new List<RzwSavingsInterestPayment>
        {
            new() { RzwSavingsAccountId = 1, RzwAmount = 10m, RzwSavingsAccount = account1 },
            new() { RzwSavingsAccountId = 1, RzwAmount = 15m, RzwSavingsAccount = account1 },
            new() { RzwSavingsAccountId = 2, RzwAmount = 20m, RzwSavingsAccount = account2 },
            new() { RzwSavingsAccountId = 3, RzwAmount = 100m, RzwSavingsAccount = account3 } // Different user
        };

        _context.RzwSavingsAccounts.AddRange(new[] { account1, account2, account3 });
        _context.RzwSavingsInterestPayments.AddRange(payments);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetUserTotalEarnedInterestAsync(userId);

        // Assert
        Assert.Equal(45m, result); // 10 + 15 + 20 = 45 RZW
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
