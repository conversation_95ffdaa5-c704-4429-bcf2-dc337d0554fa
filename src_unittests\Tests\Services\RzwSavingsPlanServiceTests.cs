using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using Xunit;

namespace RazeWinComTr.Tests.Services;

public class RzwSavingsPlanServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<RzwSavingsPlanService>> _mockLogger;
    private readonly RzwSavingsPlanService _service;

    public RzwSavingsPlanServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _context = new AppDbContext(options);

        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<RzwSavingsPlanService>>();

        _service = new RzwSavingsPlanService(_context, _mockLocalizer.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetActivePlansAsync_ShouldReturnActivePlansOrderedByDisplayOrder()
    {
        // Arrange
        var plans = new List<RzwSavingsPlan>
        {
            new() { Id = 1, Name = "Plan C", IsActive = true, DisplayOrder = 3 },
            new() { Id = 2, Name = "Plan A", IsActive = true, DisplayOrder = 1 },
            new() { Id = 3, Name = "Plan B", IsActive = false, DisplayOrder = 2 },
            new() { Id = 4, Name = "Plan D", IsActive = true, DisplayOrder = 2 }
        };

        _context.RzwSavingsPlans.AddRange(plans);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetActivePlansAsync();

        // Assert
        Assert.Equal(3, result.Count); // Only active plans
        Assert.Equal("Plan A", result[0].Name); // DisplayOrder = 1
        Assert.Equal("Plan D", result[1].Name); // DisplayOrder = 2
        Assert.Equal("Plan C", result[2].Name); // DisplayOrder = 3
    }

    [Fact]
    public async Task GetPlanByIdAsync_ValidId_ShouldReturnPlan()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            IsActive = true,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPlanByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("Test Plan", result.Name);
    }

    [Fact]
    public async Task GetPlanByIdAsync_InactivePlan_ShouldReturnNull()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Inactive Plan",
            IsActive = false,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPlanByIdAsync(1);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetPlanByTermTypeAsync_ValidTermType_ShouldReturnPlan()
    {
        // Arrange
        var plans = new List<RzwSavingsPlan>
        {
            new() { Id = 1, Name = "Daily Plan", TermType = RzwSavingsTermType.Daily, IsActive = true },
            new() { Id = 2, Name = "Monthly Plan", TermType = RzwSavingsTermType.Monthly, IsActive = true },
            new() { Id = 3, Name = "Yearly Plan", TermType = RzwSavingsTermType.Yearly, IsActive = true }
        };

        _context.RzwSavingsPlans.AddRange(plans);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPlanByTermTypeAsync(RzwSavingsTermType.Monthly);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Monthly Plan", result.Name);
        Assert.Equal(RzwSavingsTermType.Monthly, result.TermType);
    }

    [Fact]
    public async Task ValidatePlanAsync_ValidAmount_ShouldReturnTrue()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            IsActive = true,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidatePlanAsync(1, 1000m);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ValidatePlanAsync_AmountTooLow_ShouldReturnFalse()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            IsActive = true,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidatePlanAsync(1, 50m);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ValidatePlanAsync_AmountTooHigh_ShouldReturnFalse()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            IsActive = true,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidatePlanAsync(1, 15000m);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ValidatePlanAsync_NoMaxLimit_ShouldReturnTrue()
    {
        // Arrange
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            IsActive = true,
            MinRzwAmount = 100m,
            MaxRzwAmount = null // No maximum limit
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidatePlanAsync(1, 1000000m);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ValidatePlanAsync_InvalidPlanId_ShouldReturnFalse()
    {
        // Act
        var result = await _service.ValidatePlanAsync(999, 1000m);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task InitializeDefaultPlansAsync_EmptyDatabase_ShouldCreateDefaultPlans()
    {
        // Act
        await _service.InitializeDefaultPlansAsync();

        // Assert
        var plans = await _context.RzwSavingsPlans.ToListAsync();
        Assert.Equal(3, plans.Count);

        var dailyPlan = plans.FirstOrDefault(p => p.TermType == RzwSavingsTermType.Daily);
        Assert.NotNull(dailyPlan);
        Assert.Equal("RZW Daily Savings", dailyPlan.Name);
        Assert.Equal(RzwSavingsConstants.DAILY_TERM_DURATION, dailyPlan.TermDuration);

        var monthlyPlan = plans.FirstOrDefault(p => p.TermType == RzwSavingsTermType.Monthly);
        Assert.NotNull(monthlyPlan);
        Assert.Equal("RZW Monthly Savings", monthlyPlan.Name);
        Assert.Equal(RzwSavingsConstants.MONTHLY_TERM_DURATION, monthlyPlan.TermDuration);

        var yearlyPlan = plans.FirstOrDefault(p => p.TermType == RzwSavingsTermType.Yearly);
        Assert.NotNull(yearlyPlan);
        Assert.Equal("RZW Yearly Savings", yearlyPlan.Name);
        Assert.Equal(RzwSavingsConstants.YEARLY_TERM_DURATION, yearlyPlan.TermDuration);
    }

    [Fact]
    public async Task InitializeDefaultPlansAsync_ExistingPlans_ShouldNotCreateDuplicates()
    {
        // Arrange
        var existingPlan = new RzwSavingsPlan
        {
            Name = "Existing Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            IsActive = true
        };

        _context.RzwSavingsPlans.Add(existingPlan);
        await _context.SaveChangesAsync();

        // Act
        await _service.InitializeDefaultPlansAsync();

        // Assert
        var plans = await _context.RzwSavingsPlans.ToListAsync();
        Assert.Single(plans); // Should still have only the existing plan
        Assert.Equal("Existing Plan", plans[0].Name);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
