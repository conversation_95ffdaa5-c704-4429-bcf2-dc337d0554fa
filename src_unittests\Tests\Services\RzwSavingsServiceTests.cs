using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using Xunit;

namespace RazeWinComTr.Tests.Services;

public class RzwSavingsServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<RzwSavingsService>> _mockLogger;
    private readonly RzwSavingsService _service;

    public RzwSavingsServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .ConfigureWarnings(warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .EnableSensitiveDataLogging()
            .Options;
        _context = new AppDbContext(options);

        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<RzwSavingsService>>();

        // Create real services for integration testing
        var mockBalanceLogger = new Mock<ILogger<RzwBalanceManagementService>>();
        var mockTokenPriceService = new Mock<ITokenPriceService>();
        var mockWalletService = new Mock<IWalletService>();
        var mockTradeService = new Mock<TradeService>(null!, _context);

        // Setup token price service to return RZW token ID
        mockTokenPriceService.Setup(x => x.GetRzwTokenIdAsync()).ReturnsAsync(1);
        mockTokenPriceService.Setup(x => x.GetCoinInfoAsync(1)).ReturnsAsync(new RzwTokenInfo { TokenId = 1, BuyPrice = 1, SellPrice = 0.95m });
        mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync()).ReturnsAsync(new RzwTokenInfo { TokenId = 1, BuyPrice = 1, SellPrice = 0.95m });

        // Setup wallet service mocks
        mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync((int userId, int coinId) =>
            {
                var wallet = _context.Wallets.FirstOrDefault(w => w.UserId == userId && w.CoinId == coinId);
                return wallet?.Balance ?? 0;
            });

        mockWalletService.Setup(x => x.LockBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>()))
            .ReturnsAsync(true);

        mockWalletService.Setup(x => x.LockBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction>()))
            .ReturnsAsync(true);

        mockWalletService.Setup(x => x.UnlockBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>()))
            .ReturnsAsync(true);

        mockWalletService.Setup(x => x.UnlockBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction>()))
            .ReturnsAsync(true);

        mockWalletService.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>()))
            .ReturnsAsync(new Wallet());

        var realBalanceService = new RzwBalanceManagementService(_context, mockWalletService.Object, mockTokenPriceService.Object, mockTradeService.Object);

        var mockPlanLogger = new Mock<ILogger<RzwSavingsPlanService>>();
        var realPlanService = new RzwSavingsPlanService(_context, _mockLocalizer.Object, mockPlanLogger.Object);

        var mockInterestLogger = new Mock<ILogger<RzwSavingsInterestService>>();
        var realInterestService = new RzwSavingsInterestService(
            _context, realBalanceService, realPlanService, mockTradeService.Object,
            mockTokenPriceService.Object, _mockLocalizer.Object, mockInterestLogger.Object);

        _service = new RzwSavingsService(
            _context,
            realBalanceService,
            realPlanService,
            realInterestService,
            _mockLocalizer.Object,
            _mockLogger.Object);

        SetupMockLocalizer();
        SetupTestData();
    }

    private void SetupTestData()
    {
        // Add test user
        var user = new User { UserId = 1, Email = "<EMAIL>", Name = "Test User", IdentityNumber = "12345678901", Surname = "User", PhoneNumber = "1234567890", BirthDate = DateTime.Now.AddYears(-25), IsActive = 1, CrDate = DateTime.UtcNow };
        _context.Users.Add(user);

        // Add test market (RZW token)
        var rzwMarket = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZW-TRY", IsActive = 1, CrDate = DateTime.UtcNow };
        _context.Markets.Add(rzwMarket);

        // Add test wallet with sufficient balance
        var wallet = new Wallet
        {
            Id = 1,
            UserId = 1,
            CoinId = 1,
            Balance = 10000m,
            LockedBalance = 0m
        };
        _context.Wallets.Add(wallet);

        _context.SaveChanges();
    }

    private void SetupMockLocalizer()
    {
        _mockLocalizer.Setup(x => x["Invalid savings plan"])
            .Returns(new LocalizedString("Invalid savings plan", "Invalid savings plan"));
        _mockLocalizer.Setup(x => x["Amount does not meet plan requirements"])
            .Returns(new LocalizedString("Amount does not meet plan requirements", "Amount does not meet plan requirements"));
        _mockLocalizer.Setup(x => x["Insufficient available RZW balance"])
            .Returns(new LocalizedString("Insufficient available RZW balance", "Insufficient available RZW balance"));
        _mockLocalizer.Setup(x => x["Failed to lock RZW balance"])
            .Returns(new LocalizedString("Failed to lock RZW balance", "Failed to lock RZW balance"));
        _mockLocalizer.Setup(x => x["Savings account created successfully"])
            .Returns(new LocalizedString("Savings account created successfully", "Savings account created successfully"));
        _mockLocalizer.Setup(x => x["An error occurred while creating savings account"])
            .Returns(new LocalizedString("An error occurred while creating savings account", "An error occurred while creating savings account"));
        _mockLocalizer.Setup(x => x["Savings account not found"])
            .Returns(new LocalizedString("Savings account not found", "Savings account not found"));
        _mockLocalizer.Setup(x => x["Savings account is not active"])
            .Returns(new LocalizedString("Savings account is not active", "Savings account is not active"));
        _mockLocalizer.Setup(x => x["Failed to unlock RZW balance"])
            .Returns(new LocalizedString("Failed to unlock RZW balance", "Failed to unlock RZW balance"));
        _mockLocalizer.Setup(x => x["Early withdrawal completed successfully"])
            .Returns(new LocalizedString("Early withdrawal completed successfully", "Early withdrawal completed successfully"));
        _mockLocalizer.Setup(x => x["An error occurred during withdrawal"])
            .Returns(new LocalizedString("An error occurred during withdrawal", "An error occurred during withdrawal"));
    }

    [Fact]
    public async Task CreateSavingsAccountAsync_ValidInput_ShouldCreateAccount()
    {
        // Arrange
        var userId = 1;
        var planId = 1;
        var rzwAmount = 1000m;
        var plan = new RzwSavingsPlan
        {
            Id = planId,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CreateSavingsAccountAsync(userId, planId, rzwAmount);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Account);
        Assert.Equal(userId, result.Account.UserId);
        Assert.Equal(planId, result.Account.PlanId);
        Assert.Equal(rzwAmount, result.Account.RzwAmount);
        Assert.Equal(RzwSavingsStatus.Active, result.Account.Status);
    }

    [Fact]
    public async Task CreateSavingsAccountAsync_InvalidPlan_ShouldReturnFailure()
    {
        // Arrange
        var userId = 1;
        var planId = 999; // Non-existent plan ID
        var rzwAmount = 1000m;

        // Act
        var result = await _service.CreateSavingsAccountAsync(userId, planId, rzwAmount);

        // Assert
        Assert.False(result.Success);
        Assert.Null(result.Account);
        Assert.Equal("Invalid savings plan", result.Message);
    }

    [Fact]
    public async Task CreateSavingsAccountAsync_InsufficientBalance_ShouldReturnFailure()
    {
        // Arrange
        var userId = 1;
        var planId = 1;
        var rzwAmount = 1000m;
        var plan = new RzwSavingsPlan
        {
            Id = planId,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true
        };

        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();

        // Update wallet to have insufficient balance
        var wallet = await _context.Wallets.FirstAsync(w => w.UserId == userId && w.CoinId == 1);
        wallet.Balance = 500m; // Less than required 1000m
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CreateSavingsAccountAsync(userId, planId, rzwAmount);

        // Assert
        Assert.False(result.Success);
        Assert.Null(result.Account);
        Assert.Equal("Insufficient available RZW balance", result.Message);
    }

    [Fact]
    public async Task GetUserActiveSavingsAsync_ShouldReturnActiveAccounts()
    {
        // Arrange
        var userId = 1;
        var baseDate = DateTime.UtcNow;

        // Add a test plan first
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true,
            CreatedDate = baseDate
        };
        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();
        var accounts = new List<RzwSavingsAccount>
        {
            new() {
                Id = 1,
                UserId = userId,
                PlanId = 1,
                RzwAmount = 1000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddDays(-2),
                MaturityDate = baseDate.AddDays(-1),
                Status = RzwSavingsStatus.Active,
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddDays(-2)
            },
            new() {
                Id = 2,
                UserId = userId,
                PlanId = 1,
                RzwAmount = 2000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddDays(-1),
                MaturityDate = baseDate,
                Status = RzwSavingsStatus.Active,
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddDays(-1)
            },
            new() {
                Id = 3,
                UserId = userId,
                PlanId = 1,
                RzwAmount = 1500m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddDays(-3),
                MaturityDate = baseDate.AddDays(-2),
                Status = RzwSavingsStatus.Matured,
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddDays(-3)
            },
            new() {
                Id = 4,
                UserId = 2,
                PlanId = 1,
                RzwAmount = 3000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate,
                MaturityDate = baseDate.AddDays(1),
                Status = RzwSavingsStatus.Active,
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate
            }
        };

        _context.RzwSavingsAccounts.AddRange(accounts);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetUserActiveSavingsAsync(userId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, account => Assert.Equal(userId, account.UserId));
        Assert.All(result, account => Assert.Equal(RzwSavingsStatus.Active, account.Status));
        // Should be ordered by CreatedDate descending
        Assert.True(result[0].CreatedDate > result[1].CreatedDate);
    }

    [Fact]
    public async Task GetSavingsAccountAsync_ValidAccount_ShouldReturnAccount()
    {
        // Arrange
        var userId = 1;
        var accountId = 1;
        var baseDate = DateTime.UtcNow;

        // Add a test plan first
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true,
            CreatedDate = baseDate
        };
        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();
        var account = new RzwSavingsAccount
        {
            Id = accountId,
            UserId = userId,
            PlanId = 1,
            RzwAmount = 1000m,
            InterestRate = 0.001m,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            StartDate = baseDate,
            MaturityDate = baseDate.AddDays(1),
            Status = RzwSavingsStatus.Active,
            EarlyWithdrawalPenalty = 0.10m,
            CreatedDate = baseDate
        };

        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetSavingsAccountAsync(accountId, userId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(accountId, result.Id);
        Assert.Equal(userId, result.UserId);
    }

    [Fact]
    public async Task GetSavingsAccountAsync_WrongUser_ShouldReturnNull()
    {
        // Arrange
        var userId = 1;
        var wrongUserId = 2;
        var accountId = 1;
        var baseDate = DateTime.UtcNow;

        // Add a test plan first
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true,
            CreatedDate = baseDate
        };
        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();
        var account = new RzwSavingsAccount
        {
            Id = accountId,
            UserId = userId,
            PlanId = 1,
            RzwAmount = 1000m,
            InterestRate = 0.001m,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            StartDate = baseDate,
            MaturityDate = baseDate.AddDays(1),
            Status = RzwSavingsStatus.Active,
            EarlyWithdrawalPenalty = 0.10m,
            CreatedDate = baseDate
        };

        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetSavingsAccountAsync(accountId, wrongUserId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetMaturedAccountsAsync_ShouldReturnMaturedAccounts()
    {
        // Arrange
        var baseDate = DateTime.UtcNow;

        // Add a test plan first
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true,
            CreatedDate = baseDate
        };
        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();
        var accounts = new List<RzwSavingsAccount>
        {
            new() {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddDays(-2),
                Status = RzwSavingsStatus.Active,
                MaturityDate = baseDate.AddDays(-1),
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddDays(-2)
            }, // Matured
            new() {
                Id = 2,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate,
                Status = RzwSavingsStatus.Active,
                MaturityDate = baseDate.AddDays(1),
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate
            },  // Not matured
            new() {
                Id = 3,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddDays(-2),
                Status = RzwSavingsStatus.Matured,
                MaturityDate = baseDate.AddDays(-1),
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddDays(-2)
            }, // Already processed
            new() {
                Id = 4,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                InterestRate = 0.001m,
                TermType = RzwSavingsTermType.Daily,
                TermDuration = 1,
                StartDate = baseDate.AddMinutes(-2),
                Status = RzwSavingsStatus.Active,
                MaturityDate = baseDate.AddMinutes(-1),
                EarlyWithdrawalPenalty = 0.10m,
                CreatedDate = baseDate.AddMinutes(-2)
            } // Matured
        };

        _context.RzwSavingsAccounts.AddRange(accounts);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetMaturedAccountsAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, a => a.Id == 1);
        Assert.Contains(result, a => a.Id == 4);
    }

    [Fact]
    public async Task EarlyWithdrawAsync_WithEarnedInterest_ShouldCalculatePenaltyOnInterestNotPrincipal()
    {
        // Arrange
        var userId = 1;
        var accountId = 1;
        var baseDate = DateTime.UtcNow.AddDays(-10); // Account held for 10 days

        // Create a plan that allows early withdrawal interest calculation
        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Daily Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 30, // 30 day plan
            InterestRate = 0.001m, // 0.1% daily
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true
        };

        // Create a shorter plan for early withdrawal eligibility
        var shortPlan = new RzwSavingsPlan
        {
            Id = 2,
            Name = "Short Daily Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1, // 1 day plan (eligible for 10 days held)
            InterestRate = 0.0005m, // 0.05% daily
            MinRzwAmount = 100m,
            MaxRzwAmount = 10000m,
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = accountId,
            UserId = userId,
            PlanId = 1,
            RzwAmount = 1000m,
            InterestRate = 0.001m,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 30,
            StartDate = baseDate,
            MaturityDate = baseDate.AddDays(30),
            Status = RzwSavingsStatus.Active,
            EarlyWithdrawalPenalty = 0.10m, // 10% penalty
            CreatedDate = baseDate
        };

        _context.RzwSavingsPlans.AddRange(plan, shortPlan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.EarlyWithdrawAsync(accountId, userId);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("Early withdrawal completed successfully", result.Message);

        // Verify the account status was updated
        var updatedAccount = await _context.RzwSavingsAccounts.FindAsync(accountId);
        Assert.Equal(RzwSavingsStatus.Withdrawn, updatedAccount!.Status);

        // The withdrawn amount should be principal + net interest (after penalty)
        // Expected: 1000 (principal) + earned interest - penalty on earned interest
        // Since we can't easily calculate the exact interest here due to complex logic,
        // we just verify it's greater than the principal (1000)
        Assert.True(result.WithdrawnAmount >= 1000m,
            $"Withdrawn amount {result.WithdrawnAmount} should be at least the principal amount 1000");
    }

    [Fact]
    public async Task EarlyWithdrawAsync_AccountNotFound_ShouldReturnFailure()
    {
        // Arrange
        var userId = 1;
        var nonExistentAccountId = 999;

        // Act
        var result = await _service.EarlyWithdrawAsync(nonExistentAccountId, userId);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Savings account not found", result.Message);
        Assert.Equal(0m, result.WithdrawnAmount);
    }

    [Fact]
    public async Task EarlyWithdrawAsync_InactiveAccount_ShouldReturnFailure()
    {
        // Arrange
        var userId = 1;
        var accountId = 1;
        var baseDate = DateTime.UtcNow.AddDays(-1);

        var plan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Test Plan",
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            InterestRate = 0.001m,
            MinRzwAmount = 100,
            MaxRzwAmount = 10000,
            IsActive = true
        };

        var account = new RzwSavingsAccount
        {
            Id = accountId,
            UserId = userId,
            PlanId = 1,
            RzwAmount = 1000m,
            InterestRate = 0.001m,
            TermType = RzwSavingsTermType.Daily,
            TermDuration = 1,
            StartDate = baseDate,
            MaturityDate = baseDate.AddDays(1),
            Status = RzwSavingsStatus.Matured, // Inactive status
            EarlyWithdrawalPenalty = 0.10m,
            CreatedDate = baseDate
        };

        _context.RzwSavingsPlans.Add(plan);
        _context.RzwSavingsAccounts.Add(account);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.EarlyWithdrawAsync(accountId, userId);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Savings account is not active", result.Message);
        Assert.Equal(0m, result.WithdrawnAmount);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
