using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;
using RazeWinComTr.Tests.Mocks;
using Xunit;

namespace RazeWinComTr.Tests.Services;

/// <summary>
/// Unit tests for WalletService Phase 2 implementation
/// Tests the new balance management functionality including available/locked balance operations
/// </summary>
public class WalletServicePhase2Tests : TestBase
{
    /// <summary>
    /// Tests that GetUserAvailableBalanceAsync returns the correct available balance
    /// </summary>
    [Fact]
    public async Task GetUserAvailableBalanceAsync_WithExistingWallet_ReturnsCorrectBalance()
    {
        // Arrange
        var dbContext = CreateDbContext("GetUserAvailableBalanceAsync_WithExistingWallet_ReturnsCorrectBalance");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var availableBalance = await walletService.GetUserAvailableBalanceAsync(user.UserId, market.Id);

        // Assert
        Assert.Equal(100m, availableBalance);
    }

    /// <summary>
    /// Tests that GetUserLockedBalanceAsync returns the correct locked balance
    /// </summary>
    [Fact]
    public async Task GetUserLockedBalanceAsync_WithExistingWallet_ReturnsCorrectBalance()
    {
        // Arrange
        var dbContext = CreateDbContext("GetUserLockedBalanceAsync_WithExistingWallet_ReturnsCorrectBalance");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var lockedBalance = await walletService.GetUserLockedBalanceAsync(user.UserId, market.Id);

        // Assert
        Assert.Equal(50m, lockedBalance);
    }

    /// <summary>
    /// Tests that GetUserTotalBalanceAsync returns the sum of available and locked balances
    /// </summary>
    [Fact]
    public async Task GetUserTotalBalanceAsync_WithExistingWallet_ReturnsSumOfAvailableAndLocked()
    {
        // Arrange
        var dbContext = CreateDbContext("GetUserTotalBalanceAsync_WithExistingWallet_ReturnsSumOfAvailableAndLocked");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var totalBalance = await walletService.GetUserTotalBalanceAsync(user.UserId, market.Id);

        // Assert
        Assert.Equal(150m, totalBalance); // 100 + 50
    }

    /// <summary>
    /// Tests that LockBalanceAsync successfully moves balance from available to locked
    /// </summary>
    [Fact]
    public async Task LockBalanceAsync_WithSufficientAvailableBalance_MovesBalanceToLocked()
    {
        // Arrange
        var dbContext = CreateDbContext("LockBalanceAsync_WithSufficientAvailableBalance_MovesBalanceToLocked");
        var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>().Object;
        var mockTradeService = new TradeService(mockLocalizer, dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var mockRzwTokenInfo = new RzwTokenInfo { TokenId = 1, BuyPrice = 1.0m, SellPrice = 1.05m };
        var market = TestDataGenerator.CreateMarket(mockRzwTokenInfo.TokenId, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 20m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await walletService.LockBalanceAsync(user.UserId, mockRzwTokenInfo, 30m);

        // Assert
        Assert.True(result);

        var updatedWallet = await dbContext.Wallets.FirstAsync(w => w.UserId == user.UserId && w.CoinId == market.Id);
        Assert.Equal(70m, updatedWallet.Balance); // 100 - 30
        Assert.Equal(50m, updatedWallet.LockedBalance); // 20 + 30

        // Verify trade record was created
        var trades = await dbContext.Trades.Where(t => t.UserId == user.UserId && t.CoinId == market.Id).ToListAsync();
        Assert.Single(trades);
        Assert.Equal(TradeType.Buy, trades[0].Type);
        Assert.Equal(-30m, trades[0].CoinAmount);
    }

    /// <summary>
    /// Tests that LockBalanceAsync fails when insufficient available balance
    /// </summary>
    [Fact]
    public async Task LockBalanceAsync_WithInsufficientAvailableBalance_ReturnsFalse()
    {
        // Arrange
        var dbContext = CreateDbContext("LockBalanceAsync_WithInsufficientAvailableBalance_ReturnsFalse");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var mockRzwTokenInfo = new RzwTokenInfo { TokenId = 1, BuyPrice = 1.0m, SellPrice = 1.05m };
        var market = TestDataGenerator.CreateMarket(mockRzwTokenInfo.TokenId, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 50m, 20m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await walletService.LockBalanceAsync(user.UserId, mockRzwTokenInfo, 100m);

        // Assert
        Assert.False(result);

        // Verify wallet balances unchanged
        var updatedWallet = await dbContext.Wallets.FirstAsync(w => w.UserId == user.UserId && w.CoinId == market.Id);
        Assert.Equal(50m, updatedWallet.Balance);
        Assert.Equal(20m, updatedWallet.LockedBalance);
    }

    /// <summary>
    /// Tests that UnlockBalanceAsync successfully moves balance from locked to available
    /// </summary>
    [Fact]
    public async Task UnlockBalanceAsync_WithSufficientLockedBalance_MovesBalanceToAvailable()
    {
        // Arrange
        var dbContext = CreateDbContext("UnlockBalanceAsync_WithSufficientLockedBalance_MovesBalanceToAvailable");
        var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>().Object;
        var mockTradeService = new TradeService(mockLocalizer, dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var mockRzwTokenInfo = new RzwTokenInfo { TokenId = 1, BuyPrice = 1.0m, SellPrice = 0.95m };
        var market = TestDataGenerator.CreateMarket(mockRzwTokenInfo.TokenId, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 50m, 80m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await walletService.UnlockBalanceAsync(user.UserId, mockRzwTokenInfo, 30m);

        // Assert
        Assert.True(result);

        var updatedWallet = await dbContext.Wallets.FirstAsync(w => w.UserId == user.UserId && w.CoinId == market.Id);
        Assert.Equal(80m, updatedWallet.Balance); // 50 + 30
        Assert.Equal(50m, updatedWallet.LockedBalance); // 80 - 30

        // Verify trade record was created
        var trades = await dbContext.Trades.Where(t => t.UserId == user.UserId && t.CoinId == market.Id).ToListAsync();
        Assert.Single(trades);
        Assert.Equal(TradeType.Sell, trades[0].Type);
        Assert.Equal(30m, trades[0].CoinAmount);
    }

    /// <summary>
    /// Tests that GetWalletBalanceInfoAsync returns comprehensive balance information
    /// </summary>
    [Fact]
    public async Task GetWalletBalanceInfoAsync_WithExistingWallet_ReturnsCompleteInfo()
    {
        // Arrange
        var dbContext = CreateDbContext("GetWalletBalanceInfoAsync_WithExistingWallet_ReturnsCompleteInfo");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        market.Name = "RazeWin Token";
        market.IconUrl = "https://example.com/rzw.png";
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act
        var balanceInfo = await walletService.GetWalletBalanceInfoAsync(user.UserId, market.Id);

        // Assert
        Assert.Equal(user.UserId, balanceInfo.UserId);
        Assert.Equal(market.Id, balanceInfo.CoinId);
        Assert.Equal("RZWTRY", balanceInfo.CoinCode);
        Assert.Equal("RazeWin Token", balanceInfo.CoinName);
        Assert.Equal("https://example.com/rzw.png", balanceInfo.IconUrl);
        Assert.Equal(100m, balanceInfo.AvailableBalance);
        Assert.Equal(50m, balanceInfo.LockedBalance);
        Assert.Equal(150m, balanceInfo.TotalBalance);
        Assert.True(balanceInfo.HasLockedBalance);
    }

    /// <summary>
    /// Tests backward compatibility - obsolete methods should still work
    /// </summary>
    [Fact]
    public async Task BackwardCompatibility_ObsoleteMethods_StillWork()
    {
        // Arrange
        var dbContext = CreateDbContext("BackwardCompatibility_ObsoleteMethods_StillWork");
        var mockTradeService = CreateMockTradeService(dbContext);
        var mockTokenPriceService = CreateMockTokenPriceService(1.0m);

        var walletService = new WalletService(dbContext, mockTradeService, mockTokenPriceService);

        var user = TestDataGenerator.CreateUserWithEmail(1, "<EMAIL>");
        var market = TestDataGenerator.CreateMarket(1, "RZW", "RZWTRY");
        var wallet = TestDataGenerator.CreateWallet(1, user.UserId, market.Id, 100m, 50m);

        dbContext.Users.Add(user);
        dbContext.Markets.Add(market);
        dbContext.Wallets.Add(wallet);
        await dbContext.SaveChangesAsync();

        // Act & Assert - Test obsolete methods
#pragma warning disable CS0618 // Type or member is obsolete
        var oldBalance = await walletService.GetUserBalanceAsync(user.UserId, market.Id);
        var newBalance = await walletService.GetUserAvailableBalanceAsync(user.UserId, market.Id);

        Assert.Equal(oldBalance, newBalance);
        Assert.Equal(100m, oldBalance);
#pragma warning restore CS0618 // Type or member is obsolete
    }
}
